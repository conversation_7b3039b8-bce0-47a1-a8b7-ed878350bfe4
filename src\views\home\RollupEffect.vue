<template>
  <div class="app-container">
    <div class="video-container" id="video-container" @mousemove="handleMouseMove">
      <div id="player1" :style="{ width: '960px', height: '540px' }"></div>
      <div id="player2" :style="{ 
        width: '960px', 
        height: '540px',
        clipPath: clipPath 
      }"></div>
      <!-- 黄色分隔线 -->
      <div 
        v-if="showLine" 
        id="line" 
        :style="{ left: linePosition + 'px' }"
      ></div>
    </div>

    <div class="controls">
      <br><b>示例功能按钮：</b>
      <button id="bindJL" @click="bindJL">进入卷帘</button>
      <button id="unbindJL" @click="unbindJL">退出卷帘</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 响应式状态管理
const isInRollMode = ref(false);
const clipPath = ref('inset(0 0 0 0%)');
const showLine = ref(false);
const linePosition = ref(0);
const api1 = ref(null);
const api2 = ref(null);
const apiOnReady = ref(false);

// 云渲染服务器地址
const host = '***************:8080';

// 处理鼠标移动事件，更新卷帘效果
const handleMouseMove = (event) => {
  if (!isInRollMode.value) return;
  
  // 获取容器位置并计算相对坐标
  const container = document.getElementById('video-container');
  const rect = container.getBoundingClientRect();
  const x = event.clientX - rect.left;
  
  // 更新分隔线位置
  linePosition.value = x;
  showLine.value = true;
  
  // 计算裁剪百分比并应用
  const percentage = (100 * x / 960);
  clipPath.value = `inset(0 0 0 ${percentage}%)`;
};

// 进入卷帘模式
const bindJL = () => {
  isInRollMode.value = true;
  
  // 激活视频窗口
  const player2 = document.getElementById('player2');
  if (player2) {
    player2.focus();
    player2.click();
    player2.click();
  }
  
  // 禁用交互并设置相机位置
  if (api1.value && api2.value) {
    api1.value.settings.setEnableInteract(false);
    api2.value.settings.setEnableInteract(false);
    api1.value.camera.set(490256.214375, 2495216.6375, 188.812012, -31.755644, 52.429024, 0);
    api2.value.camera.set(490256.214375, 2495216.6375, 88.812012, -31.755644, 52.429024, 0);
  }
};

// 退出卷帘模式
const unbindJL = () => {
  isInRollMode.value = false;
  showLine.value = false;
  clipPath.value = 'inset(0 0 0 0%)';
  
  // 恢复交互
  if (api1.value && api2.value) {
    api1.value.settings.setEnableInteract(true);
    api2.value.settings.setEnableInteract(true);
  }
};

// 播放器1初始化完成回调
const onReady1 = () => {
  apiOnReady.value = true;
  if (api1.value) {
    api1.value.settings.setMainUIVisibility(false);
    api1.value.settings.setCampassVisible(false);
  }
};

// 播放器2初始化完成回调
const onReady2 = () => {
  apiOnReady.value = true;
  if (api2.value) {
    api2.value.settings.setMainUIVisibility(false);
    api2.value.settings.setCampassVisible(false);
  }
};

// 事件处理函数
const onEvent = (event) => {
  if (event.eventtype === 'LeftMouseButtonClick' && event.Type === 'TileLayer') {
    console.info("当前点击图层位置：" + event.MouseClickPoint);
  }

  if (event.eventtype === 'LeftMouseButtonClick' && event.Type === 'marker') {
    // 可添加标记点点击处理逻辑
  }
};

// 日志输出函数
const onLog = (s, nnl) => {
  const logStr = s + (nnl ? '' : '\n');
  console.info(logStr);
};

// 组件挂载后初始化播放器
onMounted(() => {
  if (window.DigitalTwinPlayer) {
    // 初始化第一个播放器
    const options1 = {
      'domId': 'player1',
      'id': '2551604637832',
      'ui': {
        startupInfo: false,
        statusIndicator: false,
        statusButton: false,
        fullscreenButton: false,
        homeButton: false,
        mainUI: false,
        campass: false,
      },
      'apiOptions': {
        'onReady': onReady1,
        'onLog': onLog,
        'onEvent': onEvent,
      },
    };
    
    api1.value = new window.DigitalTwinPlayer(host, options1).getAPI();
    
    // 初始化第二个播放器
    const options2 = {
      'domId': 'player2',
      'id': '2551663703769',
      'ui': {
        startupInfo: false,
        statusIndicator: false,
        statusButton: false,
        fullscreenButton: false,
        homeButton: false,
        mainUI: false,
        campass: false,
      },
      'apiOptions': {
        'onReady': onReady2,
        'onLog': onLog,
        'onEvent': onEvent,
      },
    };
    
    api2.value = new window.DigitalTwinPlayer(host, options2).getAPI();
  } else {
    console.error('DigitalTwinPlayer 未加载，请确保已引入相关库');
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

#player1 {
  position: absolute;
  top: 0px;
  left: 0px;
}

#player2 {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 10;
}

.video-container {
  position: relative;
  width: 960px;
  height: 540px;
  overflow: hidden;
  border: 1px solid #ccc;
}

.streamingVideoCursorPointer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#line {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: yellow;
  z-index: 100;
  pointer-events: none;
}

.controls {
  margin-top: 15px;
}

button {
  margin: 0 8px;
  padding: 6px 12px;
  cursor: pointer;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #359e75;
}

button:active {
  background-color: #2a8661;
}
</style>
