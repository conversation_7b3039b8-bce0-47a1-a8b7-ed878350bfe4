<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <div class="bg-layer bg-layer-3"></div>
    <!-- 地图容器，修改ID避免重复 -->
    <!-- <div id="player" class="map-container"></div> -->

    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <!-- <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" />
            <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" />
            <span class="temperature">17℃</span>
          </div>
        </div> -->

        <!-- 上下布局容器 -->
        <div class="left-content">
          <!-- 第一部分 -->

          <div class="content-layout-first">
            <div class="content-layout-header">
              <div class="weather-info-bar">
                <div class="current-weather">
                  <span class="weather-label">当前天气</span>
                  <div class="city-info">
                    <img class="location-icon" src="@/assets/images/home/<USER>" alt="" />
                    <span class="city-name"
                      >{{ weaherData.city }} &nbsp;&nbsp;{{ weaherData.minTemperature }}~{{
                        weaherData.maxTemperature
                      }}</span
                    >
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="weather-detail">
                  <div class="weather-icon-large">
                    <img src="@/assets/images/home/<USER>" alt="大雨" />
                    <div>{{ weaherData.daytimeWind }}，{{ weaherData.daytimeWeather }}</div>
                  </div>
                  <div class="weather-icon-large">
                    <img src="@/assets/images/home/<USER>" alt="大雨" />
                    <div>{{ weaherData.nighttimeWind }}，{{ weaherData.nighttimeWeather }}</div>
                  </div>
                  <!-- <div class="weather-icon-large">
                    <img src="@/assets/images/home/<USER>" alt="大雨" />
                  </div> -->
                  <!-- <div class="weather-data">
                    <div class="weather-type">
                      <span class="weather-name">大雨</span>
                      <span class="weather-temp">19优</span>
                    </div>
                    <div class="wind-info">
                      <span>东风 </span>
                      <span> 2级</span>
                    </div>
                  </div> -->
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="weather-forecast">
                  <div class="forecast-item">
                    <div class="forecast-title">未来24h天气</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">{{ weaherData.forecast_24h }}</span>
                      <!-- <span class="forecast-value-unit">mm</span> -->
                    </div>
                  </div>
                  <div class="forecast-item">
                    <div class="forecast-title">检测次数</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">{{ weaherData.monitor_count }}</span>
                      <!-- <span class="forecast-value-unit">mm</span> -->
                    </div>
                  </div>
                  <div class="forecast-item">
                    <div class="forecast-title">预警等级</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">{{ weaherData.monitor_level }}</span>
                      <!-- <span class="forecast-value-unit">mm</span> -->
                    </div>
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="publish-info">
                  <span class="publish-label" @click="yuntuClick">云图</span>
                  <span class="publish-label" @click="yuzhongkuaibao">雨中快报</span>
                  <!-- <div class="publish-times"> -->
                  <!-- <div class="time-item">开始时间: 20:50:00</div> -->
                  <!-- <div class="time-item">结束时间: 18:00:00</div> -->
                  <!-- </div> -->
                </div>
              </div>
            </div>
            <div class="content-left-wrap">
              <div class="wrap-left">
                <div class="duanlin new-add-border">
                  <div class="chart-headers touchang">
                    <div class="chart-title">短临气象</div>
                    <!-- <div class="alarm-tabs">
                      <div class="alarm-tab">12小时/一周</div>
                    </div> -->
                    <div class="title-more">12小时/一周</div>
                  </div>
                  <div ref="wrapLeftCharts" class="wrap-left-charts"></div>
                  <div class="leftbei"></div>
                  <div class="righttbei"></div>
                  <div class="bottombeibg"></div>
                </div>

                <div class="new-add-border">
                  <div class="paishuixitong">
                    <div class="chart-headers touchang">
                      <div class="chart-title">
                        易涝点报警
                        <div class="baojing-value">
                          <!-- 预警数{{ zngzYldflDATA.totalWarning }} &nbsp;&nbsp;&nbsp; 离线数{{ zngzYldflDATA.totalOffline }} -->
                          报警数 {{ zngzYldflDATA.totalWarning }}
                        </div>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      </div>
                      <!-- <div class="alarm-tabs">
                      <div class="alarm-tab">12小时/一周</div>
                    </div> -->
                      <div class="title-more">更新时间 {{ zngzYldflDATA.time }}</div>
                    </div>
                    <div class="p-s-x-t-w">
                      <div class="ps-x-tw-l">
                        <div class="x-t-item" v-for="item in zngzYldflDATA.data" @click="yilaodiannotSingleClick(item)">
                          <div class="item-i">
                            <img :src="item.icon" alt="" />
                          </div>
                          <div class="i-i-right">
                            <div class="i-i-r-name">{{ item.type }}</div>
                            <div class="i-i-r-n-v">
                              <div class="to-tal">{{ item.total }}</div>
                              /
                              <div class="on-line">{{ item.online }}</div>
                              /
                              <div class="warn-ing">{{ item.warning }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="ps-x-tw-l-r">
                        <div class="ps-x-tw-circle" @click="cicleClick(1)">
                          <div class="ps-x-tw-circle-li">
                            <div class="ps-x-tw-circle-li-d"></div>
                            <div class="ps-x-tw-circle-li-c">预报点个数:{{ zngzYldflDATA.forecast_point }}</div>
                          </div>
                        </div>
                        <div class="ps-x-tw-circle1" style="right: 100px" @click.stop="cicleClick(2)">
                          <div class="ps-x-tw-circle1-value" style="cursor: pointer" @click.stop="cicleClick(3)">
                            {{ zngzYldflDATA.forecast_alarm }}
                          </div>

                          <div class="ps-x-tw-circle-li">
                            <div class="ps-x-tw-circle-li-d"></div>
                            <div class="ps-x-tw-circle-li-c">报警点个数:{{ zngzYldflDATA.alarm_point }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="margin: 0 20px" class="bz-yx">
                    <div
                      style="
                        display: flex;
                        justify-content: flex-end;
                        height: 40px;
                        align-items: center;
                        padding-right: 140px;
                      "
                    >
                      <div
                        @click="selectLevel('一级')"
                        style="margin: 0 10px; cursor: pointer; width: 40px; height: 9px; background: red"
                      ></div>
                      <div
                        @click="selectLevel('二级')"
                        style="margin: 0 10px; cursor: pointer; width: 40px; height: 9px; background: orange"
                      ></div>
                      <div
                        @click="selectLevel('三级')"
                        style="margin: 0 10px; cursor: pointer; width: 40px; height: 9px; background: yellow"
                      ></div>
                      <div
                        @click="selectLevel('四级')"
                        style="margin: 0 10px; cursor: pointer; width: 40px; height: 9px; background: blue"
                      ></div>
                    </div>

                    <div class="bz-yx-b">
                      <!-- <div class="bz-yx-i">
                      <div class="chart-headers touchang">
                        <div class="chart-title">积水点统计</div>
                      </div>
                      <div class="bz-yx-ww">
                        <div class="yx-b-left">
                          <div class="neilao-small duibi">对比汇总</div>
                          <div class="neilao-small yubaojishui">预报积水点统计</div>
                          <div class="yx-pie" ref="bzyxtj1"></div>
                          <div class="yx-zhu" ref="bzyxtj2"></div>
                        </div>
                        <div class="yx-b-middle">
                          <div class="neilao-small duibi">监测报警点统计</div>
                          <div class="neilao-small yubaojishui">事件统计</div>
                          <div class="yx-m-pie" ref="bzyxtj3"></div>
                          <div class="yx-m-zhu" ref="bzyxtj4"></div>
                        </div>
                      </div>
                    </div> -->

                      <div class="yx-b-right">
                        <!-- <div class="chart-headers touchang">
                        <div class="chart-title">泵站运行统计</div>
                        <div class="title-more" @click="bengzhanClick">更多 ></div>
                      </div> -->
                        <div class="rain-monitor-table-container-tj">
                          <div class="rain-monitor-table-header">
                            <div class="rain-th th-index1">名称</div>
                            <div class="rain-th th-station1">类别</div>
                            <div class="rain-th th-hour1">报警时间</div>
                            <div class="rain-th th-warning1">积水深度(m)</div>
                            <div class="rain-th th-warning1">报警等级</div>
                            <div class="rain-th th-warning1">预报等级</div>
                          </div>
                          <div class="rain-monitor-table-body">
                            <div
                              class="rain-tr"
                              v-for="(row, idx) in rainMonitorTableData"
                              :key="row.index"
                              @click="bengzhanItemClick(row)"
                            >
                              <div class="rain-td th-index1">{{ row.name }}</div>
                              <div class="rain-td th-station1">{{ row.type }}</div>
                              <div class="rain-td th-hour1">{{ row.forecastTime }}</div>
                              <div class="rain-td th-warning1">{{ row.rainfall }}</div>
                              <div class="rain-td th-warning1">
                                <div style="color: red" v-if="row.alarmLevel === 'I'">{{ row.alarmLevel }}</div>
                                <div style="color: orange" v-if="row.alarmLevel === 'II'">{{ row.alarmLevel }}</div>
                                <div style="color: yellow" v-if="row.alarmLevel === 'III'">{{ row.alarmLevel }}</div>
                                <div style="color: blue" v-if="row.alarmLevel === 'IV'">{{ row.alarmLevel }}</div>
                              </div>
                              <div class="rain-td th-warning1">
                                <div style="color: red" v-if="row.reportLevel === 'I'">{{ row.reportLevel }}</div>
                                <div style="color: orange" v-if="row.reportLevel === 'II'">{{ row.reportLevel }}</div>
                                <div style="color: yellow" v-if="row.reportLevel === 'III'">{{ row.reportLevel }}</div>
                                <div style="color: blue" v-if="row.reportLevel === 'IV'">{{ row.reportLevel }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="leftbei"></div>
                  <div class="righttbei"></div>
                  <div class="bottombeibg"></div>
                </div>

                <!-- <div class="jishui">
                  <div class="jishui-left">
                    <div class="jishui-wrap">
                      <div class="chart-headers touzhong">
                        <div class="chart-title">
                          积水点信息
                          <div class="baojing-value">报警58个</div>
                        </div>
                        <div class="alarm-tabs">
                          <div
                            v-for="(tab, index) in alarmTabs"
                            :key="index"
                            class="alarm-tab"
                            :class="{ active: currentAlarmTab === tab.value }"
                            @click="changejishuipaihang(tab.value)"
                          >
                            {{ tab.name }}
                          </div>
                        </div>
                        <div class="title-more">更多 ></div>
                      </div>
                      <div class="rain-monitor-table-container">
                        <div class="rain-monitor-table-header">
                          <div class="rain-th th-index1">积水点</div>
                          <div class="rain-th th-station1">场景类型</div>
                          <div class="rain-th th-hour1">数据</div>
                          <div class="rain-th th-warning1">通讯时间</div>
                        </div>
                        <div class="rain-monitor-table-body">
                          <div class="rain-tr" v-for="(row, idx) in rainMonitorTableData" :key="row.index">
                            <div class="rain-td th-index1">{{ row.point }}</div>
                            <div class="rain-td th-station1">{{ row.type }}</div>
                            <div class="rain-td th-hour1">{{ row.data }}</div>
                            <div class="rain-td th-warning1">{{ row.time }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="bengzhan-wrap">
                      <div class="chart-headers touzhong">
                        <div class="chart-title">泵站水位</div>
                      </div>
                      <div class="rain-monitor-table-container1">
                        <div class="rain-monitor-table-header">
                          <div class="rain-th th-index1">站名</div>
                          <div class="rain-th th-station1">实时水位(m)</div>
                          <div class="rain-th th-hour1">水位变化速率(cm/h)</div>
                          <div class="rain-th th-warning1">警戒水位差值(m)</div>
                          <div class="rain-th th-warning2">实时流量(m²/s)</div>
                          <div class="rain-th th-warning3">水流速度(m/s)</div>
                        </div>
                        <div class="rain-monitor-table-body">
                          <div class="rain-tr" v-for="(row, idx) in bengzhanshuiwei" :key="row.index">
                            <div class="rain-td th-index1">{{ row.station_name }}</div>
                            <div class="rain-td th-station1">{{ row.real_time_water_level }}</div>
                            <div class="rain-td th-hour1">{{ row.water_level_change_rate }}</div>
                            <div class="rain-td th-warning1">{{ row.alert_water_level_difference }}</div>
                            <div class="rain-td th-warning2">{{ row.real_time_flow }}</div>
                            <div class="rain-td th-warning3">{{ row.water_flow_speed }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="jishui-right">
                    <div class="guanwang-container">
                      <div class="chart-headers touduan">
                        <div class="chart-title">管网预警</div>
                      </div>
                      <div class="guanwang">
                        <div class="guanwang-wrap" v-for="(gItem, gIndex) in gwyjDATA">
                          <div class="guanwang-item" :class="['active' + gIndex]">
                            <div class="guanwang-item-value">{{ gItem.warning }}</div>
                            <div class="guanwang-item-val">{{ gItem.online }}/{{ gItem.total }}</div>
                          </div>
                          <div class="guanwang-text">{{ gItem.type }}</div>
                        </div>
                      </div>
                    </div>

                    <div style="height: 34px"></div>
                    <div class="wuzi-diaopei">
                      <div class="chart-headers touduan">
                        <div class="chart-title">物资调配统计</div>

                        <div class="title-more">更多 ></div>
                      </div>
                      <div class="rain-monitor-table-container-wuzi">
                        <div class="rain-monitor-table-header">
                          <div class="rain-th th-index1">序号</div>
                          <div class="rain-th th-station1">名称</div>
                          <div class="rain-th th-hour1">数量</div>
                          <div class="rain-th th-warning1">部门</div>
                          <div class="rain-th th-warning1">地址</div>
                        </div>
                        <div class="rain-monitor-table-body">
                          <div class="rain-tr" v-for="(row, idx) in wzdptjDATA" :key="row.index">
                            <div class="rain-td th-index1">{{ row.seq }}</div>
                            <div class="rain-td th-station1">{{ row.name }}</div>
                            <div class="rain-td th-hour1">{{ row.quantity }}</div>
                            <div class="rain-td th-warning1">{{ row.department }}</div>
                            <div class="rain-td th-warning1">{{ row.address }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div> -->
              </div>
              <div class="wrap-right">
                <div class="new-add-border">
                  <div class="wrap-right-con">
                    <div class="chart-headers touchang">
                      <div class="chart-title">
                        排水设施报警
                        <div class="baojing-value">
                          <!-- 预警数{{ zngzYldflDATA.totalWarning }} &nbsp;&nbsp;&nbsp; 离线数{{ zngzYldflDATA.totalOffline }} -->
                          报警数&nbsp;{{ paishuisheshibaojing.totalWarning }}
                        </div>
                      </div>
                      <!-- <div class="alarm-tabs">
                      <div class="alarm-tab">12小时/一周</div>
                    </div> -->
                      <div class="title-more" @click="getMorePaishui">更多 ></div>
                    </div>
                    <div class="p-s-x-t-w">
                      <div
                        class="x-t-item"
                        v-for="(item, index) in paishuisheshibaojings"
                        @click="notSingleClick(item)"
                      >
                        <div class="item-i">
                          <img :src="item.icon" alt="" />
                        </div>
                        <div class="i-i-right">
                          <div class="i-i-r-name">{{ item.type }}</div>
                          <div class="i-i-r-n-v">
                            <div class="to-tal">{{ item.total }}</div>
                            /
                            <div class="on-line">{{ item.online }}</div>
                            /
                            <div class="warn-ing">{{ item.warning }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="baojingshebeixinxi">
                    <!-- <div class="chart-headers touduan"> -->
                    <!-- <div class="chart-title">报警设备信息列表</div> -->
                    <!-- <div class="title-more">更多 ></div> -->
                    <!-- </div> -->
                    <div class="rain-monitor-table-container-xinxi">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-index1">报警点名称</div>
                        <div class="rain-th th-station1">报警时间</div>
                        <div class="rain-th th-hour1">水位(m)</div>
                        <div class="rain-th th-warning1">报警等级</div>
                        <!-- <div class="rain-th th-warning1">积水深度</div> -->
                      </div>
                      <div class="rain-monitor-table-body">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in paishuisheshibaojing_table"
                          :key="row.index"
                          @click="baojingxinxiClick(row)"
                        >
                          <div class="rain-td th-index1">{{ row.name }}</div>
                          <div class="rain-td th-station1">{{ row.forecastTime }}</div>

                          <div class="rain-td th-hour1">
                            {{ row.rainfall }}
                          </div>
                          <div class="rain-td th-warning1">
                            <div style="color: red" v-if="row.alarmLevel === 'I'">{{ row.alarmLevel }}</div>
                            <div style="color: orange" v-if="row.alarmLevel === 'II'">{{ row.alarmLevel }}</div>
                            <div style="color: yellow" v-if="row.alarmLevel === 'III'">{{ row.alarmLevel }}</div>
                            <div style="color: blue" v-if="row.alarmLevel === 'IV'">{{ row.alarmLevel }}</div>
                          </div>
                          <!-- <div class="rain-td th-warning1">{{ row.water_depth }}m</div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="leftbei"></div>
                  <div class="righttbei"></div>
                  <div class="bottombeibg"></div>
                </div>
                <div class="bz-yx-tj new-add-border">
                  <div class="chart-headers touduan">
                    <div class="chart-title">
                      泵站运行统计
                      <div class="title-value">
                        总抽排能力 {{ bengzhanyunxingtongji.total_extraction }}m³/s &nbsp;&nbsp; 实时总抽排能力
                        {{ bengzhanyunxingtongji.real_extraction }}m³/s
                      </div>
                    </div>
                    <!-- <div class="title-more">更多 ></div> -->
                  </div>
                  <div class="bz-yx-bnew">
                    <div class="bz-yx-pie" ref="bzyxtj1"></div>
                    <div class="rain-monitor-table-container-new-bz">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-index1">泵站名称</div>
                        <div class="rain-th th-station1">状态</div>
                        <div class="rain-th th-hour1">累计开泵数</div>
                        <div class="rain-th th-warning1">运行时间</div>
                        <div class="rain-th th-warning1">前池水位</div>
                        <div class="rain-th th-warning1">抽排量(m³/s)</div>
                      </div>
                      <div class="rain-monitor-table-body">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bengzhanyunxingshuju"
                          :key="row.index"
                          @click="bengzhanGetmap(row, idx)"
                        >
                          <div class="rain-td th-index1">{{ row.pump_station }}</div>
                          <div class="rain-td th-station1">{{ row.status }}</div>
                          <div class="rain-td th-hour1">
                            {{ row.cumulative_start_times }}
                          </div>
                          <div class="rain-td th-warning1">{{ row.running_time }}min</div>
                          <div class="rain-td th-warning1">{{ row.forebay_water_level }}m</div>
                          <div class="rain-td th-warning1">{{ row.discharge_volume }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="leftbei"></div>
                  <div class="righttbei"></div>
                  <div class="bottombeibg"></div>
                </div>

                <div class="left-r-yingji">
                  <div class="yftop">
                    <div class="chart-headers touduan">
                      <div class="chart-title">
                        应急物资
                        <div class="baojing-value">{{ zngzYjwzDATA.total }}个</div>
                      </div>
                      <div class="title-more" @click="yingjiMoreClick">更多 ></div>
                    </div>
                    <div class="p-s-x-t-w-r">
                      <div class="x-t-item" v-for="item in zngzYjwzDATAs" @click="wuItemClick(item)">
                        <div class="x-t-item-wrap">
                          <img :src="item.icon" alt="雨量图标" />
                          <div>
                            <div class="item-i">{{ item.type }}</div>
                            <div class="i-i-right">
                              <div class="i-i-r-name">{{ item.quantity }}</div>
                              <div class="i-i-r-n-v">{{ item.unit }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="shebeibaojing">
                    <div class="chart-headers touduan">
                      <div class="chart-title">防汛能力</div>
                      <div class="title-more">更新时间 &nbsp; {{ fxnlDATA.time }}</div>
                    </div>
                    <div class="sbbj-i">
                      <div class="sbbj-i-i">
                        <div class="sbbj-left">
                          <div class="sbbj-p">{{ fxnlDATA?.personnel?.online_rate }}</div>
                          <div class="sbbj-v">在线率</div>
                        </div>
                        <div class="sbbj-right" @click="fangxunrenyuanClick">
                          <div class="sbbjup">
                            <div class="sbbj-up-t">防汛人员</div>
                            <div class="sbbj-up-1">{{ fxnlDATA?.personnel?.total }}</div>
                            <div class="sbbj-up-p">人</div>
                          </div>
                          <div class="sbbjdown">
                            <div class="sbbjdownxia">
                              <div class="sbbjdownt">在线人数</div>
                              <div class="sbbjdownp">
                                {{ fxnlDATA?.personnel?.online }}{{ fxnlDATA?.personnel?.unit_online }}
                              </div>
                            </div>
                            <div class="sbbjline"></div>
                            <div class="sbbjdownxia">
                              <div class="sbbjdownt">离线人数</div>
                              <div class="sbbjdownp">
                                {{ fxnlDATA?.personnel?.offline }}{{ fxnlDATA?.personnel?.unit_offline }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="sbbj-i-i">
                        <div class="sbbj-left">
                          <div class="sbbj-p">{{ fxnlDATA?.vehicles?.online_rate }}</div>
                          <div class="sbbj-v">在线率</div>
                        </div>
                        <div class="sbbj-right" @click="fangxuncheliang">
                          <div class="sbbjup">
                            <div class="sbbj-up-t">防汛车辆</div>
                            <div class="sbbj-up-1">{{ fxnlDATA.vehicles?.total }}</div>
                            <div class="sbbj-up-p">台</div>
                          </div>
                          <div class="sbbjdown">
                            <div class="sbbjdownxia">
                              <div class="sbbjdownt">在线数</div>
                              <div class="sbbjdownp">
                                {{ fxnlDATA.vehicles?.online }}{{ fxnlDATA.vehicles?.unit_online }}
                              </div>
                            </div>
                            <div class="sbbjline"></div>
                            <div class="sbbjdownxia">
                              <div class="sbbjdownt">离线数</div>
                              <div class="sbbjdownp">
                                {{ fxnlDATA.vehicles?.offline }}{{ fxnlDATA.vehicles?.unit_offline }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="sbbj-i-i">
                        <div class="sbbj-left">
                          <div class="sbbj-p">{{ fxnlDATA?.pump_truck?.online_rate }}</div>
                          <div class="sbbj-v">在线率</div>
                        </div>
                        <div class="sbbj-right">
                          <div class="sbbjup">
                            <div class="sbbj-up-t">防汛泵车</div>
                            <div class="sbbj-up-1">{{ fxnlDATA?.pump_truck?.total }}</div>
                            <div class="sbbj-up-p">台</div>
                          </div>
                          <div class="sbbjdown">
                            <div class="sbbjdownxia">
                              <div class="sbbjdownt">在线数</div>
                              <div class="sbbjdownp">
                                {{ fxnlDATA?.pump_truck?.online }}{{ fxnlDATA?.pump_truck?.unit_online }}
                              </div>
                            </div>
                            <div class="sbbjline"></div>
                            <div class="sbbjdownxia">
                              <div class="sbbjdownt">离线数</div>
                              <div class="sbbjdownp">
                                {{ fxnlDATA?.pump_truck?.offline }}{{ fxnlDATA?.pump_truck?.unit_offline }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <!-- <div class="section-title">内涝安全预警监测综合驾驶舱系统</div> -->
        <!-- 导航按钮 -->
        <!-- <div class="nav-buttons">
          <div
            v-for="(btn, index) in navButtons"
            :key="index"
            :class="['nav-button', { active: activeNavButton === index }]"
            @click="activeNavButton = index"
          >
            <div class="nav-button-text">{{ btn.text }}</div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 上部分：用户信息和返回门户 -->
        <!-- <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>
        </div> -->

        <!-- 下部分：新增的内容区域 -->
        <div class="right-content">
          <div class="right-video">
            <div class="chart-headers touzhong">
              <div class="chart-title">
                视频在线
                <div class="title-value">({{ onlineVideos }}/{{ totalVideos }})</div>
              </div>
            </div>
            <div class="video-contents">
              <div class="video-change">
                <div
                  v-for="item in viewTabs"
                  :class="{ videoActive: vidoTabFlag === item.value }"
                  @click="switchTab(item.value)"
                >
                  {{ item.name }}
                </div>
              </div>
              <div class="video-bofang">
                <div class="video-bofang-wrap" v-for="(item, index) in displayedVideos">
                  <div class="bofang">
                    <video style="width: 100%; height: 100%" :id="`video-player-${index}`" controls playsinline></video>
                  </div>
                  <div @click="handleVideoClick(item)" style="padding-left: 40px">
                    <div class="bf-addr">西中环南延</div>
                    <div class="jiang-text">当前水位(cm):0 降雨量(mm/h):0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="video-besides">
            <div class="diaodu-zl-tj new-add-border">
              <div class="chart-headers touzhong">
                <div class="chart-title">调度指令统计</div>
              </div>
              <div class="c-r-t-cc">
                <div
                  class="c-r-t-cc-i"
                  v-for="(item, index) in diaoduzhilingtongji"
                  :class="{ 'cc-i-active': paihongquFlag === index }"
                  @click="paihongquClick(item, index)"
                >
                  <div class="c-r-t-cc-t">{{ item.name }}</div>
                  <div class="c-r-t-cc-bb">{{ item.value }}</div>
                </div>
              </div>
              <div class="rain-monitor-table-container-zhiling">
                <div class="rain-monitor-table-header">
                  <div class="rain-th th-index1">指令类别</div>
                  <div class="rain-th th-station1">调度类别</div>
                  <div class="rain-th th-hour1">指令发布时间</div>
                  <div class="rain-th th-warning1">调度内容</div>
                  <div class="rain-th th-warning1">调度次数</div>
                </div>
                <div class="rain-monitor-table-body">
                  <div
                    class="rain-tr"
                    v-for="(row, idx) in diaoduzhilingData"
                    :key="row.index"
                    @click="diaoduzhilingItemClick(idx)"
                  >
                    <div class="rain-td th-index1">{{ row.command_type }}</div>
                    <div class="rain-td th-station1">{{ row.adjustment_type }}</div>
                    <div class="rain-td th-hour1">
                      {{ row.command_release_time }}
                    </div>
                    <div class="rain-td th-warning1">{{ row.adjustment_content }}</div>
                    <div class="rain-td th-warning1">{{ row.adjustment_times }}</div>
                  </div>
                </div>
              </div>
              <div style="height: 310px; display: flex">
                <div class="diaoduzhixingtop">
                  <div class="diaodu-fl-w" style="height: 310px">
                    <div class="charts-diaodu-fl" ref="diaodufl1"></div>
                    <div class="charts-diaodu-fl" ref="diaodufl2"></div>
                  </div>
                </div>
                <div class="diaoduzhixingtop">
                  <div class="charts-diaodu-f" ref="diaoduRef"></div>
                </div>
              </div>

              <div class="leftbei"></div>
              <div class="righttbei"></div>
              <div class="bottombeibg"></div>
            </div>
            <div class="right-gather">
              <div class="diaoduzhixing new-add-border">
                <div class="diaodubottom">
                  <div class="chart-headers touzhong">
                    <div class="chart-title">事件处置</div>
                    <div class="title-more" @click="caseInformationGET">更多 ></div>
                    <!-- <div class="title-more" @click="yuzhongkuaibao">雨中快报</div> -->
                  </div>
                  <div class="rain-monitor-table-container-right-bottom-dd">
                    <div class="rain-monitor-table-header">
                      <div class="rain-th th-index1">事件处置部门</div>
                      <div class="rain-th th-station1">事件来源</div>
                      <div class="rain-th th-hour1">发生时间</div>
                      <div class="rain-th th-warning1">事件名称</div>
                      <div class="rain-th th-warning2">事件状态</div>
                      <div class="rain-th th-warning3">事件位置</div>
                    </div>
                    <div class="rain-monitor-table-body">
                      <div
                        class="rain-tr"
                        v-for="(row, idx) in caseInformationDATA"
                        :key="row.index"
                        @click="singleMarker(row)"
                      >
                        <div class="rain-td th-index1">{{ row.eventRepDepartName }}</div>
                        <div class="rain-td th-station1">{{ row.reportUserName }}</div>
                        <div class="rain-td th-hour1">{{ row.rptTime }}</div>
                        <div class="rain-td th-warning1" :title="row.eventName">{{ row.eventName }}</div>
                        <div class="rain-td th-warning2" :title="row.eventStatusName">{{ row.eventStatusName }}</div>
                        <div class="rain-td th-warning3" :title="row.eventGridName">{{ row.eventGridName }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="leftbei"></div>
                <div class="righttbei"></div>
                <div class="bottombeibg bottom-left"></div>
              </div>
              <div class="yingji-fangyu new-add-border">
                <!-- <div class="diaoduzhixingtop">
                  <div class="charts-diaodu-f" ref="diaoduRef"></div>
                </div> -->

                <div class="fangxun-w-yuqing">
                  <div class="chart-headers touduan">
                    <div class="chart-title">舆情信息</div>
                    <div class="title-more" @click="yuqingMore">更多 ></div>
                  </div>
                  <div class="yuqing-i">
                    <div class="yuqing-w" v-for="item in yqxxgetDATA">
                      <div class="yuqing-u">{{ item.title }}</div>
                      <div class="yuqing-b">{{ item.time }}</div>
                      <div class="yuqing-status" v-if="item.status === '已核实'">{{ item.status }}</div>
                      <div class="yuqing-status1" v-else>{{ item.status }}</div>
                    </div>
                  </div>
                </div>
                <div class="leftbei"></div>
                <div class="righttbei"></div>
                <div class="bottombeibg bottom-left"></div>
              </div>

              <!-- <div class="zhongtai">
                <div class="chart-headers touzhong">
                  <div class="chart-title">中台指令</div>
                  <div class="title-more">更多 ></div>
                </div>
                <div class="rain-monitor-table-container-right">
                  <div class="rain-monitor-table-header">
                    <div class="rain-th th-index1">序号</div>
                    <div class="rain-th th-station1">事件内容</div>
                    <div class="rain-th th-hour1">时间</div>
                    <div class="rain-th th-warning1">地点</div>
                    <div class="rain-th th-warning1">事件等级</div>
                  </div>
                  <div class="rain-monitor-table-body">
                    <div class="rain-tr" v-for="(row, idx) in ztzlDATA" :key="row.index">
                      <div class="rain-td th-index1">{{ row.seq }}</div>
                      <div class="rain-td th-station1" :title="row.event">{{ row.event }}</div>
                      <div class="rain-td th-hour1" :title="row.time">{{ row.time }}</div>
                      <div class="rain-td th-warning1">{{ row.location }}</div>
                      <div class="rain-td th-warning1">{{ row.alert_level }}</div>
                    </div>
                  </div>
                </div>
              </div> -->
            </div>
            <div class="xietong">
              <!-- <div class="chart-headers touchang">
                <div class="chart-title">协同处置事件</div>
                <div class="title-more">雨中快报</div>
              </div>
              <div class="rain-monitor-table-container-right-bottom">
                <div class="rain-monitor-table-header">
                  <div class="rain-th th-index1">事件处置部门</div>
                  <div class="rain-th th-station1">上报人</div>
                  <div class="rain-th th-hour1">事件发生时间</div>
                  <div class="rain-th th-warning1">事件名称</div>
                  <div class="rain-th th-warning2">事件状态</div>
                  <div class="rain-th th-warning3">事件位置</div>
                </div>
                <div class="rain-monitor-table-body">
                  <div class="rain-tr" v-for="(row, idx) in caseInformationDATA" :key="row.index">
                    <div class="rain-td th-index1">{{ row.eventRepDepartName }}</div>
                    <div class="rain-td th-station1">{{ row.reportUserName }}</div>
                    <div class="rain-td th-hour1">{{ row.rptTime }}</div>
                    <div class="rain-td th-warning1" :title="row.eventName">{{ row.eventName }}</div>
                    <div class="rain-td th-warning2" :title="row.eventStatusName">{{ row.eventStatusName }}</div>
                    <div class="rain-td th-warning3" :title="row.eventGridName">{{ row.eventGridName }}</div>
                  </div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <el-dialog
    v-model="wuzidialogVisible"
    modal-class="supplies-dialog"
    title="应急物资"
    width="1500"
    :modal="false"
    :show-close="false"
    :draggable="true"
  >
    <div style="margin-top: 40px">
      <div class="heaader-close" @click="wuzidialogVisible = false">x</div>
      <el-form size="large" :inline="true" :model="formInlinewuzi" class="demo-form-inline" style="margin-left: 10px">
        <el-form-item label="名称">
          <el-input style="width: 300px" v-model="formInlinewuzi.wzTypeName" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="仓库">
          <el-select
            style="width: 300px"
            :popper-append-to-body="true"
            popper-class="select-popper"
            v-model="formInlinewuzi.cbdId"
            placeholder="请选择"
            clearable
          >
            <el-option :label="item.name" :key="item.id" v-for="item in changkuvalue" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmitwuzi">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="wuzitableData"
        height="650"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        style="width: 100%; margin-bottom: 32px; background: transparent"
        @row-click="rowClickTable"
      >
        <el-table-column prop="wzlx" label="名称" />
        <el-table-column prop="ckname" label="仓库" />
        <el-table-column prop="jldw" label="单位" />
        <el-table-column prop="sl" label="数量" />
      </el-table>

      <el-pagination
        v-model:current-page="wuziPageNum"
        v-model:page-size="wuziPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="wuziTotal"
        @size-change="handleSizeChangewuzi"
        @current-change="handleCurrentChangewuzi"
      />
    </div>
  </el-dialog>

  <el-dialog
    modal-class="supplies-dialog"
    :show-close="false"
    v-model="yuqingdialogVisible"
    title="舆情信息"
    width="1500"
  >
    <div style="margin-top: 50px">
      <div class="heaader-close" @click="yuqingdialogVisible = false">x</div>
      <el-table
        class="yuqing-table-wrap"
        :data="yuqingtableData"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        height="700"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column width="900" prop="title" label="名称" />
        <el-table-column prop="handleStatusName" label="状态" />
        <el-table-column prop="createTime" label="时间" />
      </el-table>

      <el-pagination
        v-model:current-page="yuqingPageNum"
        v-model:page-size="yuqingPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="yuqingTotal"
        @size-change="handleSizeChangeyuqing"
        @current-change="handleCurrentChangeyuqing"
      />
    </div>
  </el-dialog>

  <el-dialog
    modal-class="supplies-dialog"
    :show-close="false"
    v-model="shijiandialogVisible"
    title="事件处置"
    width="1500"
  >
    <div style="margin-top: 40px; font-size: 24px">
      <div class="heaader-close" @click="shijiandialogVisible = false">x</div>
      <el-form
        size="large"
        :inline="true"
        :model="formInlineshijian"
        class="demo-form-inline"
        style="margin-left: 10px"
      >
        <el-form-item label="区域">
          <el-select
            style="width: 300px"
            :popper-append-to-body="true"
            popper-class="select-popper"
            v-model="formInlineshijian.eventGridName"
            placeholder="请选择"
            clearable
          >
            <el-option :label="item.name" :key="item.id" v-for="item in shijianOptions" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            v-model="formInlineshijian.rptTime"
            type="date"
            placeholder="请选择"
            :size="size"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitshijian">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="shijiantableData"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        height="650"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column prop="eventRepDepartName" label="事件处置部门" />
        <el-table-column prop="reportUserName" label="事件来源" />
        <el-table-column prop="rptTime" label="发生时间" />
        <el-table-column prop="eventName" label="事件名称" />
        <el-table-column prop="eventStatusName" label="事件状态" />
        <el-table-column prop="eventGridName" label="事件位置" />
      </el-table>

      <el-pagination
        v-model:current-page="shijianPageNum"
        v-model:page-size="shijianPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="shijianTotal"
        @size-change="handleSizeChangeshijian"
        @current-change="handleCurrentChangeshijian"
      />
    </div>
  </el-dialog>

  <el-dialog modal-class="supplies-dialog" v-model="bengzhandialogVisible" title="泵站运行统计" width="1500">
    <div style="font-size: 24px">
      <div class="new-header-title">
        <div class="header-name">泵站运行统计</div>
        <div class="heaader-close" @click="bengzhandialogVisible = false">x</div>
      </div>
      <el-form
        size="large"
        :inline="true"
        :model="formInlinebengzhan"
        class="demo-form-inline"
        style="margin-left: 10px"
      >
        <el-form-item label="名称">
          <el-input style="width: 300px" v-model="formInlinebengzhan.name" placeholder="请输入" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitbengzhan">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="bengzhantableData"
        height="650"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column prop="name" label="泵站名称" />
        <el-table-column prop="bztype" label="泵管类型" />
        <el-table-column prop="bzxz" label="泵管性质" />
        <el-table-column prop="pck" label="排出⼝" />
        <el-table-column prop="bgq" label="泵管区" />
        <el-table-column prop="jcsjn" label="建成时间（年）" />
      </el-table>

      <el-pagination
        v-model:current-page="bengzhanPageNum"
        v-model:page-size="bengzhanPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="bengzhanTotal"
        @size-change="handleSizeChangebengzhan"
        @current-change="handleCurrentChangebengzhan"
      />
    </div>
  </el-dialog>

  <el-dialog
    :modal="false"
    :draggable="true"
    modal-class="supplies-dialog fangxunrenyuan"
    v-model="fangxundialogVisible"
    title="防汛车辆"
    :show-close="false"
    width="1500"
  >
    <div class="leftbei"></div>
    <div class="righttbei"></div>
    <div class="bottombeibg"></div>
    <div style="margin-top: 40px; font-size: 24px">
      <div class="heaader-close" @click="fangxundialogVisible = false">x</div>
      <el-form
        size="large"
        :inline="true"
        :model="formInlinefangxun"
        class="demo-form-inline"
        style="margin-left: 10px"
      >
        <el-form-item label="车牌号">
          <el-input style="width: 300px" v-model="formInlinefangxun.cphm" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="驾驶⼈姓名">
          <el-input style="width: 300px" v-model="formInlinefangxun.jsrxm" placeholder="请输入" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitfangxun">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="fangxuntableData"
        height="650"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        @row-click="handleClickfangxuancheliang"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column prop="cphm" label="车牌号" />
        <el-table-column prop="cllx" label="车辆类型" />
        <el-table-column prop="clpp" label="⻋辆品牌" />
        <el-table-column prop="jsrxm" label="驾驶⼈姓名" />
        <el-table-column prop="newDhhm" label="驾驶⼈联系电话" />
        <el-table-column prop="ssbm" label="所属部⻔" />
        <el-table-column prop="zrrxm" label="责任⼈姓名" />
      </el-table>

      <el-pagination
        v-model:current-page="fangxunPageNum"
        v-model:page-size="fangxunPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="fangxunTotal"
        @size-change="handleSizeChangefangxun"
        @current-change="handleCurrentChangefangxun"
      />
    </div>
  </el-dialog>

  <el-dialog
    :modal="false"
    :draggable="true"
    modal-class="supplies-dialog fangxunrenyuan"
    v-model="fangxunrenyuandialogVisible"
    title="防汛人员"
    :show-close="false"
    width="1500"
  >
    <div class="leftbei"></div>
    <div class="righttbei"></div>
    <div class="bottombeibg"></div>
    <div style="margin-top: 40px; font-size: 24px">
      <div class="heaader-close" @click="fangxunrenyuandialogVisible = false">x</div>
      <el-form
        size="large"
        :inline="true"
        :model="formInlinefangxunrenyuan"
        class="demo-form-inline"
        style="margin-left: 10px"
      >
        <el-form-item label="负责人">
          <el-input style="width: 300px" v-model="formInlinefangxunrenyuan.leader" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="负责人联系电话">
          <el-input
            style="width: 300px"
            v-model="formInlinefangxunrenyuan.leaderPhone"
            placeholder="请输入"
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitfangxunrenyuan">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="fangxunrenyuantableData"
        height="650"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        @row-click="handleClickfangxuancheliang"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column prop="teamName" label="队伍名称" />
        <el-table-column prop="teamDuty" label="队伍职责" />
        <el-table-column prop="leader" label="负责人" />
        <el-table-column prop="leaderPhones" label="负责人联系电话" />
        <el-table-column prop="responsibleUnit" label="责任单位" />
        <el-table-column prop="reservePointAddress" label="储备点地址" />
        <el-table-column prop="locationArea" label="所在区域" />
      </el-table>

      <el-pagination
        v-model:current-page="fangxunrenyuanPageNum"
        v-model:page-size="fangxunrenyuanPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="fangxunrenyuanTotal"
        @size-change="handleSizeChangefangxunrenyuan"
        @current-change="handleCurrentChangefangxunrenyuan"
      />
    </div>
  </el-dialog>

  <el-dialog
    modal-class="supplies-dialog"
    v-model="paishuidialogVisible"
    title="排水设施报警"
    width="1500"
    :modal="true"
    :draggable="true"
    :show-close="false"
    :before-close="handlePaishuidialogClose"
    class="custom-dialog supplies-dialog"
  >
    <div style="margin-top: 40px; font-size: 24px">
      <div class="heaader-close" @click="paishuidialogVisible = false">x</div>
      <el-form
        size="large"
        :inline="true"
        :model="formInlinepaishui"
        class="demo-form-inline"
        style="margin-left: 10px"
      >
        <el-form-item label="区域">
          <el-select
            style="width: 300px"
            :popper-append-to-body="true"
            popper-class="select-popper"
            v-model="formInlinepaishui.area"
            placeholder="请选择区域"
            clearable
          >
            <el-option :label="item.name" :key="item.id" v-for="item in paishuiAreaOptions" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            style="width: 300px"
            :popper-append-to-body="true"
            popper-class="select-popper"
            v-model="formInlinepaishui.type"
            placeholder="请选择类型"
            clearable
          >
            <el-option
              :label="item.type"
              :key="item.type"
              v-for="item in paishuisheshibaojing.data"
              :value="item.type"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitpaishui">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        :data="paishuitableData"
        height="650"
        @row-click="paishuiRowClick"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column prop="name" label="报警点名称" />
        <el-table-column width="400" prop="forecastTime" label="报警时间" />
        <el-table-column width="200" prop="rainfall" label="水位(位)" />
        <el-table-column width="200" prop="rainfall" label="报警等级">
          <template #default="{ row }">
            <div style="color: red" v-if="row.alarmLevel === 'I'">{{ row.alarmLevel }}</div>
            <div style="color: orange" v-if="row.alarmLevel === 'II'">{{ row.alarmLevel }}</div>
            <div style="color: yellow" v-if="row.alarmLevel === 'III'">{{ row.alarmLevel }}</div>
            <div style="color: blue" v-if="row.alarmLevel === 'IV'">{{ row.alarmLevel }}</div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="paishuiPageNum"
        v-model:page-size="paishuiPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="paishuiTotal"
        @size-change="handleSizeChangepaishui"
        @current-change="handleCurrentChangepaishui"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getCameraList, getLocation2, getLocation } from '@/api/home'
import Hls from 'hls.js'
import {
  yjdwGETperson,
  getLocationsingle,
  zngzYldbjNEW,
  statisticsNEW,
  zngzFdzltj2NEW,
  statisticsNEWdialog,
  statisticsNEWdialogWithParams,
  zngzPsssbjNEW,
  zngzNzyxtjNEW,
  zngzBzsjxxNEW,
  zngzFxnlNEW,
  zngzFdzltjNEW,
  zngzDdlbtjNEW,
  caseInfoNEW,
  //
  shortGet,
  jsdxxOnline,
  jsdxxOffline,
  jsdxxRanking,
  bzswGET,
  gwyjGET,
  wzdptjGET,
  bjxxGET,
  bjsbxxlbGET,
  ddzxgkGET,
  ztzlGET,
  caseInfoGET,
  dlqxGET,
  zngzYldfl,
  zngzBzyxzttj,
  zngzBzyxsjtj,
  zngzBzqcswtj,
  bjsbxxlb,
  yjcbdTable,
  fkclxxbGET,
  yqxxget,
  fxnl,
  zngzBzcpltj,
  zngzBjlyfl,
  zngzYldtj,
  zngzSwbjstj,
  zngzBjtj,
  zngzYjwz,
  yqxxgetPage,
  caseInformationPage,
  ddzxgk,
  bzxxbGET,
  cstqGET,
  yingjiwuziTable,
  wzwzlxGET,
  zngzBzsjxx
} from '@/api/intelligence'
const emit = defineEmits(['tagClicked', 'yuntuClickShow', 'clickTable', 'videoClicked'])
const navButtons = [{ text: '综合展示' }, { text: '智能感知' }, { text: '报警决策' }, { text: '辅助决策' }]
const activeNavButton = ref(1)
// 获取assets静态资源
const getAssetsFile = url => {
  return new URL(`../../assets/images/home/<USER>
}

const paihongquFlag = ref(0)
const sbdbqkDATA = ref({})
const diaoduzhilingISclick = ref(false)
const paihongquClick = (item, index) => {
  fangxunrenyuanF()
  diaoduzhilingISclick.value = true
  paihongquFlag.value = index
  zngzFdzltj2NEW(item.name).then(res => {
    console.log('指令调度统计的表格内容', JSON.parse(res.data[0].value))
    diaoduzhilingData.value = JSON.parse(res.data[0].value).data
    diaoduzhilingData.value.forEach((data, dataindex) => {
      data.dataIndex = dataindex
    })
    if (paihongquFlag.value !== 0) {
      emit('ddCommand', { locationData: diaoduzhilingData.value })
    }
  })
}
const diaoduzhilingItemClick = row => {
  console.log(row, '点击调度指令单个item')
  if (paihongquFlag.value === 0) {
    return
  }
  if (diaoduzhilingISclick.value) {
    fdapi.marker.focus('DDZL_' + row, 1200, 0)
  } else {
    emit('ddCommand', { locationData: diaoduzhilingData.value, index: row })
  }
}

const selectLevel = level => {
  yilaodianlevel.value = level
  yilaodianbaojingf()
}
const cicleClick = item => {
  cateData.value = item
  yilaodianbaojingf()
}
// 时间和日期
const currentTime = ref('')
const currentDate = ref('')
const fangxundialogVisible = ref(false)
const fangxunrenyuandialogVisible = ref(false)
const wuzidialogVisible = ref(false)
const bengzhandialogVisible = ref(false)
const yuqingdialogVisible = ref(false)
const shijiandialogVisible = ref(false)
const paishuidialogVisible = ref(false)

const wuziTotal = ref(0)
const yuqingTotal = ref(0)
const shijianTotal = ref(0)
const wuzitableData = ref([
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  }
])
const yuqingtableData = ref()
const shijiantableData = ref()
const bengzhantableData = ref()
const paishuitableData = ref()
const bengzhanPageNum = ref(1)
const fangxunPageNum = ref(1)
const fangxunrenyuanPageNum = ref(1)
const bengzhanPageSize = ref(20)
const fangxunPageSize = ref(20)
const fangxunrenyuanPageSize = ref(20)
const bengzhanTotal = ref(0)
const fangxunTotal = ref(0)
const fangxunrenyuanTotal = ref(0)
const paishuiPageNum = ref(1)
const paishuiPageSize = ref(20)
const paishuiTotal = ref(0)
const formInlinepaishui = ref({
  area: '',
  type: ''
})
const paishuiAreaOptions = ref([
  { name: '尖草坪区', id: 1 },
  { name: '杏花岭区', id: 2 },
  { name: '小店区', id: 3 },
  { name: '迎泽区', id: 4 },
  { name: '万柏林区', id: 5 },
  { name: '晋源区', id: 6 }
])
const paishuiTypeOptions = ref([
  { name: '雨水管网', value: '雨水管网' },
  { name: '污水管网', value: '污水管网' },
  { name: '泵站设施', value: '泵站设施' },
  { name: '排水口', value: '排水口' },
  { name: '调蓄设施', value: '调蓄设施' }
])
const formInlinewuzi = ref({
  wzTypeName: '',
  cbdId: ''
})
const formInlinefangxun = ref({
  cphm: '',
  jsrxm: ''
})
const formInlinefangxunrenyuan = ref({
  leader: '',
  leaderPhone: ''
})
const shijianOptions = ref([
  { name: '尖草坪区', value: '尖草坪区' },
  { name: '杏花岭区', value: '杏花岭区' },
  { name: '小店区', value: '小店区' },
  { name: '迎泽区', value: '迎泽区' },
  { name: '万柏林区', value: '万柏林区' },
  { name: '晋源区', value: '晋源区' }
])
const formInlineshijian = ref({
  rptTime: '',
  eventGridName: ''
})
const formInlinebengzhan = ref({
  name: ''
})
const fangxuntableData = ref([])
const fangxunrenyuantableData = ref([])

let timer = null

// 视频点击
const handleVideoClick = item => {
  console.log(item, '视频item')
  item.device_name = ''
  emit('videoClicked', { tableData: item })
}
// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year} ${month} ${day}`
}

// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  apiOptions: {
    onReady: function () {
      console.info('此时可以调API了')
    }
  }
})

const api = ref(null)
// 太原数字孪生内网地址
const host = ref(import.meta.env.VITE_DTS_URL)
// 组件挂载时启动定时器和初始化屏幕适配

const zngzYldflDATA = ref({})
const zngzYldflDATACOPY = ref([])
const paishuisheshibaojing = ref({})
const paishuisheshibaojings = ref({})

const paishuisheshibaojing_table = ref([])
const bengzhanyunxingtongji = ref({})
const bengzhanyunxingshuju = ref([])

const zngzBzyxzttjDATA = ref()
const zngzBzyxzttjREF = ref()
const baojingshebeixinxiliebiao = ref()
const ddzxgkDATAs = ref()
const zngzYjwzDATA = ref({})
const zngzYjwzDATAs = ref({})
const fxnlDATA = ref({})
const yqxxgetDATA = ref()
const zngzSwbjstjDATA = ref()
const zngzBjtjFunction = name => {
  zngzBjtj(name).then(res => {
    if (res.data.length) {
      baojingshebeixinxiliebiao.value = JSON.parse(res.data[0].value).data
      console.log(JSON.parse(res.data[0].value).data, '报警设备信息列表', res)
      baojingshebeixinxiliebiao.value.forEach(item => {
        // item.xcoordinate
      })
      // emit('clickScene', { tagData: {}, locationData: baojingshebeixinxiliebiao.value })
      if (name) {
        // const delieverData = []
        // baojingshebeixinxiliebiao.value.forEach(item => {
        //   const jwd = item.coordinate.split(',')
        //   delieverData.push({
        //     xcoordinate: jwd[0],
        //     ycoordinate: jwd[1],
        //     category: item.type,
        //     id: item.uuid
        //   })
        // })
        // emit('clickScene', { tagData: {}, locationData: baojingshebeixinxiliebiao.value })
        // emit('tagClicked', { tagData: {}, locationData: delieverData })
      }
    } else {
      baojingshebeixinxiliebiao.value = []
    }
    console.log('报警设备信息列表', res)
  })
}
const baojingxinxiClick = row => {
  console.log(row, '当前单个设备报警数据')
  if (paishuisheshibaojingData.value.length) {
    fdapi.marker.focus(row.yldId, 1200, 0)
  } else {
    row.sbname = row.name
    emit('clickScene', { tagData: { currentSingleData: true }, locationData: [row] })
  }

  // fdapi.marker.focus(row.yldId, 1200, 0)
}
onMounted(async () => {
  updateTime() // 立即执行一次
  timer = setInterval(updateTime, 1000) // 每秒更新一次

  // zngzBzsjxx().then(res => {
  //   console.log(res, JSON.parse(res.data[0].value).data, '泵站运行统计表格')
  //   rainMonitorTableData.value = JSON.parse(res.data[0].value).data
  // })
  // fxnl().then(res => {
  //   console.log(res, JSON.parse(res.data[0].value), '防汛能力')
  //   fxnlDATA.value = JSON.parse(res.data[0].value)
  // })
  zngzYldtj().then(res => {
    console.log(res, JSON.parse(res.data[0].value), '统计')
    // fxnlDATA.value = JSON.parse(res.data[0].value)
    const xtjDATA = JSON.parse(res.data[0].value).data
    const bjtjData = {
      tooltip: {
        trigger: 'item',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      },
      series: [
        {
          name: '',
          type: 'pie',
          center: ['50%', '40%'],
          radius: ['30%', '60%'],
          data: xtjDATA,
          label: {
            color: '#fff',
            fontSize: 20,
            formatter: function (params) {
              return `${params.name} \n ${params.value}`
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    const gwRefIns = echarts.init(gwRef.value)
    gwRefIns.setOption(bjtjData)
    gwRefIns.on('click', function (params) {
      console.log(params, '报警设备单个数据详情', xtjDATA)
      zngzBjtjFunction(xtjDATA[params.dataIndex].name)
    })
  })

  zngzSwbjstj().then(res => {
    console.log(res, JSON.parse(res.data[0].value), '根据水位报警')
    zngzSwbjstjDATA.value = JSON.parse(res.data[0].value).data
  })
  yqxxget().then(res => {
    console.log(res, JSON.parse(res.data[0].value).opinion_information, '舆情信息')
    yqxxgetDATA.value = JSON.parse(res.data[0].value).opinion_information
  })
  zngzYjwz().then(res => {
    console.log(res, JSON.parse(res.data[0].value).data, '应急物资')
    zngzYjwzDATA.value = JSON.parse(res.data[0].value)
    zngzYjwzDATAs.value = JSON.parse(res.data[0].value).data.slice(0, -3)
  })
  ddzxgk().then(res => {
    console.log(res, JSON.parse(res.data[0].value).scheduling_execution, '调度方式')
    ddzxgkDATAs.value = JSON.parse(res.data[0].value).scheduling_execution
  })
  zngzBjlyfl().then(res => {
    console.log(res, JSON.parse(res.data[0].value).data, '报警来源分类')
    const bjtjData = {
      tooltip: {
        trigger: 'item',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      },

      series: [
        {
          name: '来源分类',
          type: 'pie',
          center: ['48%', '50%'],
          radius: ['30%', '60%'],
          data: JSON.parse(res.data[0].value).data.source,
          label: {
            color: '#fff',
            fontSize: 20,
            formatter: function (params) {
              return `${params.name}\n${params.value}`
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    echarts.init(diaodufl1.value).setOption(bjtjData)
    const bjtjData1 = {
      tooltip: {
        trigger: 'item',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      },
      series: [
        {
          name: '',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['30%', '60%'],
          data: JSON.parse(res.data[0].value).data.status,
          label: {
            color: '#fff',
            fontSize: 20,
            formatter: function (params) {
              return `${params.name}\n${params.value}`
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    echarts.init(diaodufl2.value).setOption(bjtjData1)
  })
  // zngzYldfl().then(res => {
  //   console.log(JSON.parse(res.data[0].value), '排水系统')
  //   zngzYldflDATA.value = JSON.parse(res.data[0].value)
  //   zngzYldflDATACOPY.value = [...JSON.parse(res.data[0].value).data.slice(0, 6)]
  // })
  zngzBjtjFunction()

  zngzBzyxsjtj().then(res => {
    console.log(res, JSON.parse(res.data[0].value).data, '预报积水点统计')
    const chartData = JSON.parse(res.data[0].value)
    const colors = []
    for (let i = 0; i < chartData.data.length; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#00A2EC' // 最左边
          },
          {
            offset: 0.5,
            color: '#098FCF' // 左边的右边 颜色
          },
          {
            offset: 0.5,
            color: '#00A2EC' // 右边的左边 颜色
          },
          {
            offset: 1,
            color: '#00A2EC'
          }
        ]
      })
    }
    const option = {
      grid: {
        left: '14%',
        right: '4%',
        top: '27%',
        bottom: '15%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          fontSize: 22
        },
        formatter: '{b}: {c} ' + chartData.unit
      },
      xAxis: {
        type: 'category',
        data: chartData.data.map(item => item.name),
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 18,
          interval: 0,
          margin: 16,
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(0, 4) + '..'
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#fff',
          fontSize: 18,
          padding: [0, 0, 10, 30]
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 18
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.data.map(item => item.value),
          type: 'bar',
          barWidth: barWidth,
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          },
          label: {
            show: true,
            position: 'center',
            color: '#fff',
            fontSize: 20,
            offset: [10, 20]
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: chartData.data.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: chartData.data.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colors[params.dataIndex % colors.length].colorStops[0].color
              }
            }
          }
        }
      ]
    }
    nextTick(() => {
      const bzyxtj2DOM = echarts.init(bzyxtj2.value)
      bzyxtj2DOM.setOption(option)
      // bzyxtj2DOM.on('click', function (params) {
      //   console.log(params, '运行时间统计')
      //   setMarkers(chartData.data[params[dataIndex]])
      // })
    })
  })

  zngzBzqcswtj().then(res => {
    const chartData = JSON.parse(res.data[0].value).data
    const unitValue = JSON.parse(res.data[0].value).unit
    console.log(JSON.parse(res.data[0].value).data, '监测报警点统计')

    const colors = []
    for (let i = 0; i < chartData.length; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#00A2EC' // 最左边
          },
          {
            offset: 0.5,
            color: '#098FCF' // 左边的右边 颜色
          },
          {
            offset: 0.5,
            color: '#00A2EC' // 右边的左边 颜色
          },
          {
            offset: 1,
            color: '#00A2EC'
          }
        ]
      })
    }
    const option = {
      grid: {
        left: '12%',
        right: '4%',
        top: '32%',
        bottom: '20%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          fontSize: 22
        },
        formatter: '{b}: {c} ' + unitValue
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 18,
          interval: 0,
          margin: 16,
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(0, 4) + '..'
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#fff',
          fontSize: 18,
          padding: [0, 0, 10, 30]
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 18
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.map(item => item.value),
          type: 'bar',
          barWidth: barWidth,
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          },
          label: {
            show: true,
            position: 'center',
            color: '#fff',
            fontSize: 20,
            offset: [10, 20]
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colors[params.dataIndex % colors.length].colorStops[0].color
              }
            }
          }
        }
      ]
    }
    nextTick(() => {
      const bzyxtj3DOM = echarts.init(bzyxtj3.value)
      bzyxtj3DOM.setOption(option)
      // bzyxtj3DOM.on('click', function (params) {
      //   console.log(params, '前池水位都点击事件')
      //   setMarkers(chartData[params[dataIndex]])
      // })
    })
  })

  zngzBzcpltj().then(res => {
    const chartData = JSON.parse(res.data[0].value).data
    const unitValue = JSON.parse(res.data[0].value).unit
    console.log(JSON.parse(res.data[0].value).data, '事件统计')

    const colors = []
    for (let i = 0; i < chartData.length; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#00A2EC' // 最左边
          },
          {
            offset: 0.5,
            color: '#098FCF' // 左边的右边 颜色
          },
          {
            offset: 0.5,
            color: '#00A2EC' // 右边的左边 颜色
          },
          {
            offset: 1,
            color: '#00A2EC'
          }
        ]
      })
    }
    const option = {
      grid: {
        left: '12%',
        right: '4%',
        top: '20%',
        bottom: '15%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          fontSize: 22
        },
        formatter: '{b}: {c} ' + unitValue
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 18,
          interval: 0,
          margin: 16,
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(0, 4) + '..'
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#fff',
          fontSize: 18,
          padding: [0, 0, 10, 0]
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 18
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.map(item => item.value),
          type: 'bar',
          barWidth: barWidth,
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          },
          label: {
            show: true,
            position: 'center',
            color: '#fff',
            fontSize: 20,
            offset: [10, 20]
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colors[params.dataIndex % colors.length].colorStops[0].color
              }
            }
          }
        }
      ]
    }
    nextTick(() => {
      echarts.init(bzyxtj4.value).setOption(option)
    })
  })
  // zngzBzyxzttj().then(res => {
  //   console.log(res, JSON.parse(res.data[0].value).data, '运行统计')
  //   // zngzYldflDATA.value = JSON.parse(res.data[0].value).data
  //   const bjtjData = {
  //     tooltip: {
  //       trigger: 'item',
  //       color: '#000',
  //       textStyle: { color: '#fff', fontSize: 22 },
  //       borderWidth: 0,
  //       axisPointer: { type: 'shadow' },
  //       backgroundColor: 'rgba(0, 0, 0, 0.5)'
  //     },

  //     series: [
  //       {
  //         name: '',
  //         type: 'pie',
  //         center: ['51%', '50%'],
  //         radius: ['30%', '60%'],
  //         data: JSON.parse(res.data[0].value).data,
  //         label: {
  //           color: '#fff',
  //           fontSize: 20
  //           // formatter: function (params) {
  //           //   return `${params.name} \n ${params.value}`
  //           // }
  //         },
  //         emphasis: {
  //           itemStyle: {
  //             shadowBlur: 10,
  //             shadowOffsetX: 0,
  //             shadowColor: 'rgba(0, 0, 0, 0.5)'
  //           }
  //         }
  //       }
  //     ]
  //   }
  //   echarts.init(bzyxtj1.value).setOption(bjtjData)
  // })
  // 修改这里，接入地图配置
  // try {
  //   // 确保先引入了ac.min.js
  //   if (typeof acapi !== 'undefined') {
  //     // 创建数字孪生平台实例
  //     console.log('加载飞渡')
  //     api.value = new DigitalTwinPlayer(host.value, options.value)
  //     console.log('数字孪生平台初始化成功')
  //   } else {
  //     console.error('ac.min.js未正确加载，请检查引入路径')
  //   }
  // } catch (error) {
  //   console.error('数字孪生平台初始化失败:', error)
  // }
})

// 切换标签
const switchTab = tab => {
  vidoTabFlag.value = tab
  // 切换标签后重新获取对应标签的视频数据
  getCameraListData()
}

const paishuiClick = async item => {
  const result = await getLocation2({ pageSize: 1000, sstype: item.type })
  console.log(result, 'result')
  emit('tagClicked', { tagData: item, locationData: result.rows })
}
const setMarkers = item => {
  if (item.coordinate) {
    const coordinateArr = item.coordinate.split(',')
    if (coordinateArr.length === 2) {
      const emitObj = {
        locationData: [
          {
            xcoordinate: coordinateArr[0],
            ycoordinate: coordinateArr[1]
          }
        ]
      }
      emit('tagClicked', emitObj)
    }
  }
}
const singleMarker = item => {
  if (item.coordinate) {
    emit('clickTable', { tableData: { coordinate: item.coordinate, device_name: '' } })
  }
}
// 易涝点联动地图的数据
const yilaodianditudata = ref([])
const yilaodiannotSingleClick = async item => {
  paishuisheshibaojingData.value = []
  console.log(item, 'item')
  yilaodiantype.value = item.type
  yilaodianbaojingf(item.type)
  const result = await getLocation({ pageSize: 1000, sstype: item.type })
  console.log(result, 'result')
  yilaodianditudata.value = result.rows
  emit('clickScene', { tagData: item, locationData: result.rows })
}
const paishuisheshibaojingData = ref([])
// 排水设施报警点击事件
const notSingleClick = async item => {
  yilaodianditudata.value = []
  console.log(item, 'item')
  paishuixitongbaojingf(item.type)
  const result = await getLocation({ pageSize: 1000, sstype: item.type })
  console.log(result, 'result')
  paishuisheshibaojingData.value = result.rows
  emit('clickScene', { tagData: item, locationData: result.rows })
  // emit('tagClicked', { tagData: item, locationData: result.rows })
}
// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 销毁DTS实例
  if (api.value) {
    api.value = null
  }
})

const vidoTabFlag = ref('1')
const viewTabs = [
  { name: '易涝点', value: '1' },
  { name: '泵站', value: '2' },
  { name: '综治', value: '3' },
  { name: '地铁', value: '4' }
]
const alarmTabs = [
  { name: '全部', value: 'rain' },
  { name: '在线', value: 'water' },
  { name: '离线', value: 'flood' },
  { name: '积水排行', value: 'rank' }
]
const currentAlarmTab = ref('rain')
const wrapLeftCharts = ref(null)
let wrapLeftChartsInstance = null
const getLeftFirstCharts = data => {
  if (!wrapLeftCharts.value) return

  // 销毁已存在的图表实例
  if (wrapLeftChartsInstance) {
    wrapLeftChartsInstance.dispose()
  }

  // 创建新的图表实例
  wrapLeftChartsInstance = echarts.init(wrapLeftCharts.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      color: '#000',
      textStyle: { color: '#fff', fontSize: 22 },
      borderWidth: 0,
      axisPointer: { type: 'shadow' },
      backgroundColor: 'rgba(0, 0, 0, 0.5)'
      // formatter: '{a} <br/>{b} : {d}%'
    },

    xAxis: {
      type: 'category',
      axisLabel: {
        color: '#fff',
        fontSize: 23
      },
      axisLine: {
        show: false,
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      data: data.map(item => item.time)
    },
    grid: {
      left: '2%',
      right: '1%',
      bottom: '8%',
      top: '10%',
      containLabel: true
    },
    yAxis: [
      {
        type: 'value',
        name: '', // 添加单位
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
          padding: [0, 0, 0, 40] // 调整单位位置
        },
        axisLabel: {
          color: '#fff',
          fontSize: 23
        },
        axisLine: {
          lineStyle: {
            width: 0,
            color: '#D3D3D3'
          }
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.2)'
          }
        }
      },
      {
        type: 'value',
        name: '', // 添加单位
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
          padding: [0, 0, 0, 40]
        },
        axisLabel: {
          color: '#fff',
          fontSize: 20,
          formatter: '{value} °C' // 或者在label中直接格式化
        },
        axisLine: {
          lineStyle: {
            width: 0,
            color: '#D3D3D3'
          }
        }
      }
    ],
    series: [
      {
        name: '降雨量',
        data: data.map(item => item.rainfall),
        type: 'line',
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(80,135,236,0.26)' // 折线底下区域渐变色
                },
                {
                  offset: 0.8,
                  color: 'rgba(80,135,236,0)' // 折线底下区域渐变色
                }
              ],
              false
            )
          }
        }
      },
      {
        name: '气温',
        data: data.map(item => item.temperature),
        type: 'line',
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(88,247,172,0.26)' // 折线底下区域渐变色
                },
                {
                  offset: 0.8,
                  color: 'rgba(216,216,216,0)' // 折线底下区域渐变色
                }
              ],
              false
            )
          }
        }
      }
    ]
  }

  // 设置图表配置并渲染
  wrapLeftChartsInstance.setOption(option)

  // 确保图表铺满容器
  //   wrapLeftChartsInstance.value.resize()

  // 添加窗口大小变化监听器
  window.addEventListener('resize', () => {
    if (wrapLeftChartsInstance) {
      wrapLeftChartsInstance.resize()
    }
  })
}

const rainMonitorTableData = ref()

const displayedVideos = ref([])
const totalVideos = ref(0)
const onlineVideos = ref(0)
const getCameraListData = async () => {
  const result = await getCameraList({ pageSize: 6 }) // 先获取足够多的数据，再前端分页
  if (result.code === 200) {
    displayedVideos.value = result.rows
    totalVideos.value = result.total

    // 计算没有urlData的视频数量
    const noUrlDataCount = displayedVideos.value.filter(video => !video.urlData).length
    // 计算在线视频数量
    onlineVideos.value = totalVideos.value - noUrlDataCount

    console.log('getCameraListData-----------监控视频接口', result)
    // 使用nextTick确保DOM已更新后再初始化视频播放器
    nextTick(() => {
      displayedVideos.value.forEach((item, index) => {
        try {
          // 获取视频元素
          const videoElement = document.getElementById(`video-player-${index}`)
          if (!videoElement) {
            console.error(`无法找到视频元素 #video-player-${index}`)
            return
          }

          // 解析urlData字符串
          let videoUrl = ''
          if (item.urlData) {
            try {
              // 先解析外层JSON
              const urlDataObj = JSON.parse(item.urlData)
              // 再解析内层url字符串
              if (urlDataObj && urlDataObj.url) {
                const urlObj = JSON.parse(urlDataObj.url)
                // 获取flv格式的视频URL
                videoUrl = urlObj.hls
              }
            } catch (error) {
              console.error(`解析视频URL数据失败:`, error)
            }
          }

          if (videoUrl) {
            const hls = new Hls({
              // 配置HLS选项以减少请求频率
              maxBufferLength: 30,
              maxMaxBufferLength: 60,
              maxBufferSize: 60 * 1000 * 1000,
              enableWorker: true,
              enableSoftwareAES: true,
              manifestLoadingTimeOut: 10000,
              manifestLoadingMaxRetry: 1,
              fragLoadingTimeOut: 20000,
              fragLoadingMaxRetry: 3,
              progressive: false,
              lowLatencyMode: false
            })

            // 使用代理路径
            const proxyUrl = videoUrl
            hls.loadSource(proxyUrl)
            hls.attachMedia(videoElement)

            // 监听错误事件
            hls.on(Hls.Events.ERROR, (event, data) => {
              if (data.fatal) {
                console.warn(`HLS播放器错误 (视频${index}):`, data)
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    hls.startLoad()
                    break
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    hls.recoverMediaError()
                    break
                  default:
                    hls.destroy()
                    break
                }
              }
            })

            //hls.on(Hls.Events.MANIFEST_PARSED, () => video.play());
          } else {
            console.warn(`视频 ${index} 的URL为空`)
          }
        } catch (error) {
          console.error(`初始化视频播放器 ${index} 时出错:`, error)
        }
      })
    })
  }
}

// 初始化FLV播放器
const initFlvPlayers = () => {
  if (!displayedVideos.value || !displayedVideos.value.length) return

  if (flvjs.isSupported()) {
    // 为每个视频元素创建一个flv播放器
    displayedVideos.value.forEach((item, index) => {
      try {
        // 获取视频元素
        const videoElement = document.getElementById(`video-player-${index}`)
        if (!videoElement) {
          console.error(`无法找到视频元素 #video-player-${index}`)
          return
        }

        // 解析urlData字符串
        let videoUrl = ''
        // if (item) {
        //   try {
        //     // 先解析外层JSON
        //     const urlDataObj = JSON.parse(item)
        //     // 再解析内层url字符串
        //     if (urlDataObj && urlDataObj.url) {
        //       const urlObj = JSON.parse(urlDataObj.url)
        //       // 获取flv格式的视频URL
        //       videoUrl = urlObj.flv
        //     }
        //   } catch (error) {
        //     console.error(`解析视频URL数据失败:`, error)
        //   }
        // }

        // 检查视频URL是否存在
        // if (!videoUrl) {
        //   console.error(`视频 ${index} 没有有效的URL`)
        //   return
        // }

        if (videoUrl) {
          const hls = new Hls()
          // 使用代理路径
          const proxyUrl = videoUrl
          hls.loadSource(proxyUrl)
          hls.attachMedia(videoElement)
          //hls.on(Hls.Events.MANIFEST_PARSED, () => video.play());
        } else {
          console.warn(`视频 ${index} 的URL为空`)
        }
      } catch (error) {
        console.error(`初始化视频播放器 ${index} 时出错:`, error)
      }
    })
  } else {
    console.error('您的浏览器不支持flv.js')
  }
}

const diaoduRef = ref(null)
const diaoduGetEcharts = data => {
  const chartDiaodu = echarts.init(diaoduRef.value)

  let barWidth = 54
  let myData1 = data.map(item => item.name)
  let syjcl = data.map(item => item.value)
  let dbyp = new Array(syjcl.length).fill(Math.max(...syjcl) * 1)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        textStyle: {
          color: '#fff'
        }
      },
      textStyle: {
        color: '#fff',
        fontSize: 24
      },
      backgroundColor: 'rgba(17,95,182,0.5)', //设置背景颜色
      borderColor: 'rgba(255, 255, 255, .8)',
      confine: true,
      formatter: '{b}: {c}'
    },
    // legend: {
    //   icon: 'rect',
    //   itemWidth: 14,
    //   itemHeight: 4,
    //   itemGap: 20,
    //   right: '14%',
    //   top: '4%',
    //   textStyle: {
    //     fontSize: '14px',
    //     color: '#EFF7FF'
    //   },
    //   data: ['食用菌产量'],
    //   selectedMode: false
    // },
    grid: {
      top: '12%',
      left: '4%',
      right: '4%',
      bottom: '2%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: myData1,
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        color: '#fff',
        interval: 0,
        fontSize: 24,
        align: 'center'
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#A2A2A2'
        }
      },
      splitLine: {
        type: 'dashed'
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        // name: '亿元',
        splitNumber: 4,
        type: 'value',
        nameTextStyle: {
          color: '#fff',
          fontSize: 20,
          align: 'center',
          padding: [0, 28, 4, 0]
        },
        axisLabel: {
          formatter: '{value}',
          textStyle: {
            fontSize: 22,
            color: '#fff',
            lineHeight: 16
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        //下半截柱状图
        name: '2020',
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        itemStyle: {
          //lenged文本
          opacity: 0.7,
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: '#3267AB' // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#67AFFC' // 100% 处的颜色
              }
            ],
            false
          )
        },
        label: {
          normal: {
            show: false,
            position: 'top',
            formatter: e => {
              // return e.value + '次';
              return e.value
            },
            fontSize: '14px',
            color: '#2EADFB',
            fontFamily: 'siyuan',
            fontWeight: 'bold',
            offset: [0, -5]
          }
        },
        data: syjcl
      },
      {
        // 替代柱状图 默认不显示颜色，是最下方柱图（邮件营销）的value值 - 20
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        stack: '广告',
        itemStyle: {
          color: 'transparent'
        },
        data: syjcl
      },
      {
        // 最大值，顶部圆片
        name: '',
        type: 'pictorialBar',
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, -barWidth / 4],
        z: 12,
        symbolPosition: 'end',
        itemStyle: {
          color: '#0B3F67',
          opacity: 0.2
        },
        data: dbyp
      },
      {
        // 中间圆片
        name: '',
        type: 'pictorialBar',
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, -barWidth / 4],
        z: 12,
        itemStyle: {
          opacity: 1,
          color: '#2E8EDD'
        },
        symbolPosition: 'end',
        data: syjcl
      },
      {
        // 背景柱体
        name: '2019',
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        z: 0,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 1,
                color: '#0B3F67' // 0% 处的颜色
              },
              {
                offset: 0,
                color: '#0B3F67' // 100% 处的颜色
              }
            ],
            false
          ),
          opacity: 0.2
        },
        data: dbyp
      }
    ]
  }

  chartDiaodu.setOption(option)
}

// 积水点切换
const changejishuipaihang = val => {
  console.log(val, 'val')
  currentAlarmTab.value = val
  if (val === 'rain') {
    rainMonitorTableData.value = JSON.parse(JSON.stringify(jishuiquanbu.value))
  } else if (val === 'water') {
    rainMonitorTableData.value = jishuizaixian.value
  } else if (val === 'flood') {
    rainMonitorTableData.value = jishuilixian.value
  } else if (val === 'rank') {
    rainMonitorTableData.value = jishuipaihang.value
  }
}

const jishuiquanbu = ref([])
const jishuizaixian = ref([])
const jishuilixian = ref([])
const jishuipaihang = ref([])

const bengzhanshuiwei = ref([])
const gwyjDATA = ref([])
const wzdptjDATA = ref([])
const bjxxDATA = ref({})
const bjsbxxlbDATA = ref([])
const ddzxgkDATA = ref([])
const ztzlDATA = ref([])
const caseInformationDATA = ref([])
const dlqxDATA = ref()

const bzyxtj1 = ref()
const bzyxtj2 = ref()
const bzyxtj3 = ref()
const bzyxtj4 = ref()
const gwRef = ref()
const diaodufl1 = ref()
const diaodufl2 = ref()
const diaoduzhilingtongji = ref([])

let barWidth = 54
const yxtjzhu = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      textStyle: {
        color: '#fff'
      }
    },
    textStyle: {
      color: '#fff'
    },
    backgroundColor: 'rgba(17,95,182,0.5)', //设置背景颜色
    borderColor: 'rgba(255, 255, 255, .8)',
    confine: true,
    formatter: '{b}: {c}亿元'
  },
  legend: {
    icon: 'rect',
    itemWidth: 14,
    itemHeight: 4,
    itemGap: 20,
    right: '14%',
    top: '4%',
    textStyle: {
      fontSize: '14px',
      color: '#EFF7FF'
    },
    data: ['食用菌产量'],
    selectedMode: false
  },
  grid: {
    top: '16%',
    left: '4%',
    right: '4%',
    bottom: '2%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['粮食', '食用菌', '果品', '中药材', '蔬菜', '汉麻'],
    axisPointer: {
      type: 'shadow'
    },
    axisLabel: {
      color: '#fff',
      interval: 0,
      fontSize: '14px',
      align: 'center'
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(127, 214, 255, .4)'
      }
    },
    splitLine: {
      show: false
    },
    axisTick: {
      show: false
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '亿元',
      splitNumber: 4,
      type: 'value',
      nameTextStyle: {
        color: '#fff',
        fontSize: '14px',
        align: 'center',
        padding: [0, 28, 4, 0]
      },
      axisLabel: {
        formatter: '{value}',
        textStyle: {
          fontSize: '14px',
          color: '#fff',
          lineHeight: 16
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(28, 130, 197, .3)',
          type: 'dashed'
        }
      }
    }
  ],
  series: [
    {
      //下半截柱状图
      name: '2020',
      type: 'bar',
      barWidth: barWidth,
      barGap: '-100%',
      itemStyle: {
        //lenged文本
        opacity: 0.7,
        color: new echarts.graphic.LinearGradient(
          0,
          0,
          1,
          0,
          [
            {
              offset: 0,
              color: 'rgba(81, 175, 255, 1)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(81, 175, 255, 0.7)' // 100% 处的颜色
            }
          ],
          false
        )
      },
      label: {
        normal: {
          show: true,
          position: 'top',
          formatter: e => {
            // return e.value + '次';
            return e.value
          },
          fontSize: '14px',
          color: '#2EADFB',
          fontFamily: 'siyuan',
          fontWeight: 'bold',
          offset: [0, -5]
        }
      },
      data: [6.4, 5.1, 10.4, 6.4, 7.2, 4.5]
    },
    {
      // 替代柱状图 默认不显示颜色，是最下方柱图（邮件营销）的value值 - 20
      type: 'bar',
      barWidth: barWidth,
      barGap: '-100%',
      stack: '广告',
      itemStyle: {
        color: 'transparent'
      },
      data: [6.4, 5.1, 10.4, 6.4, 7.2, 4.5]
    },
    {
      // 最大值，顶部圆片
      name: '',
      type: 'pictorialBar',
      symbolSize: [barWidth, barWidth / 2],
      symbolOffset: [0, -barWidth / 4],
      z: 12,
      symbolPosition: 'end',
      itemStyle: {
        color: 'rgba(81, 175, 255, 0.1)',
        opacity: 1
      },
      data: [12, 12, 12, 12, 12, 12]
    },
    {
      // 中间圆片
      name: '',
      type: 'pictorialBar',
      symbolSize: [barWidth, barWidth / 2],
      symbolOffset: [0, -barWidth / 4],
      z: 12,
      itemStyle: {
        opacity: 1,
        color: 'rgba(81, 175, 255)'
      },
      symbolPosition: 'end',
      data: [6.4, 5.1, 10.4, 6.4, 7.2, 4.5]
    },
    {
      // 背景柱体
      name: '2019',
      type: 'bar',
      barWidth: barWidth,
      barGap: '-100%',
      z: 0,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(
          0,
          0,
          1,
          0,
          [
            {
              offset: 0,
              color: 'rgba(81, 175, 255, 1)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(0, 64, 124, 1)' // 100% 处的颜色
            }
          ],
          false
        ),
        opacity: 0.2
      },
      data: [12, 12, 12, 12, 12, 12]
    }
  ]
})
const changkuvalue = ref()
const weaherData = ref({})
const yilaodianlevel = ref('')
const cateData = ref('')
const yilaodiantype = ref('')
const yilaodianbaojingf = str => {
  statisticsNEW(yilaodiantype.value, cateData.value, yilaodianlevel.value).then(res => {
    console.log(res, '预警统计--低洼路段')
    rainMonitorTableData.value = res.rows
  })
}
const paishuixitongbaojingf = str => {
  statisticsNEW(str).then(res => {
    console.log(res, '排水系统--低洼路段')
    paishuisheshibaojing_table.value = res.rows
  })
}
// 关闭排水设施报警弹窗
const handlePaishuidialogClose = () => {
  paishuidialogVisible.value = false
}

const onSubmitpaishui = () => {
  console.log('排水设施查询条件:', formInlinepaishui.value)
  paishuiPageNum.value = 1
  paishuiPage()
}

// 获取排水设施报警数据
const paishuiPage = () => {
  const params = {
    pageNum: paishuiPageNum.value,
    pageSize: paishuiPageSize.value
  }

  // 添加查询条件
  if (formInlinepaishui.value.area) {
    params.area = formInlinepaishui.value.area
  }
  if (formInlinepaishui.value.type) {
    params.type = formInlinepaishui.value.type
  }

  statisticsNEWdialogWithParams(params)
    .then(res => {
      paishuiTotal.value = res.total
      paishuitableData.value = res.rows
      res.rows.forEach(item => {
        item.sbname = item.name
      })
      emit('clickScene', { tagData: {}, locationData: res.rows })
    })
    .catch(error => {
      console.error('获取排水设施报警数据失败:', error)
    })
}
const handleSizeChangepaishui = size => {
  paishuiPageNum.value = 1
  paishuiPageSize.value = size
  paishuiPage()
}
const handleCurrentChangepaishui = page => {
  paishuiPageNum.value = page
  paishuiPage()
}
const getMorePaishui = () => {
  paishuidialogVisible.value = true
  paishuiPage()
}
const diaoduzhilingData = ref([])
const bengzhanGetmap = (item, indexdx) => {
  console.log(item, 'item')
  item.sbname = item.pump_station
  bengzhanyunxingshuju.value.forEach(data => {
    data.sbname = data.pump_station
    data.yldId = data.sbcode
  })
  emit('clickScene', { tagData: { indexdx }, locationData: bengzhanyunxingshuju.value })
}
onMounted(async () => {
  cstqGET().then(res => {
    console.log(res, '天气', JSON.parse(res.data[0].value))
    if (res.code === 200 && res.data.length) {
      weaherData.value = JSON.parse(res.data[0].value)
    }
  })

  zngzYldbjNEW().then(res => {
    console.log(res, '易涝点报警', JSON.parse(res.data[0].value))
    zngzYldflDATA.value = JSON.parse(res.data[0].value)
    if (zngzYldflDATA.value.data.length) {
      yilaodiantype.value = zngzYldflDATA.value.data[0].type
      yilaodianbaojingf(zngzYldflDATA.value.data[0].type)
    }
  })

  zngzNzyxtjNEW().then(res => {
    console.log(res, '泵站运行统计——', JSON.parse(res.data[0].value))
    bengzhanyunxingtongji.value = JSON.parse(res.data[0].value)
    const bjtjData = {
      tooltip: {
        trigger: 'item',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%'
      },

      series: [
        {
          name: '',
          type: 'pie',
          center: ['49%', '50%'],
          radius: ['20%', '40%'],
          data: bengzhanyunxingtongji.value.data,
          label: {
            color: '#fff',
            fontSize: 20,
            formatter: function (params) {
              return `${params.name} \n ${params.value}`
            }
          },
          labelLine: {
            normal: {
              length: 12,
              length2: 10,
              lineStyle: {
                width: 1
              }
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    echarts.init(bzyxtj1.value).setOption(bjtjData)
  })

  zngzBzsjxxNEW().then(res => {
    console.log(res, '泵站运行的数据信息——', JSON.parse(res.data[0].value).data)
    bengzhanyunxingshuju.value = JSON.parse(res.data[0].value).data
  })
  zngzPsssbjNEW().then(res => {
    console.log(res, '排水系统报警__', JSON.parse(res.data[0].value))
    paishuisheshibaojing.value = JSON.parse(res.data[0].value)
    paishuisheshibaojings.value = JSON.parse(res.data[0].value).data.slice(0, -1)
    if (paishuisheshibaojing.value.data.length) {
      paishuixitongbaojingf(paishuisheshibaojing.value.data[0].type)
    }
  })

  zngzFxnlNEW().then(res => {
    console.log(res, '防汛能力__', JSON.parse(res.data[0].value))
    fxnlDATA.value = JSON.parse(res.data[0].value)
  })
  zngzFdzltjNEW().then(res => {
    console.log(res, '调度指令统计————', JSON.parse(res.data[0].value))
    diaoduzhilingtongji.value = JSON.parse(res.data[0].value).data
    if (diaoduzhilingtongji.value.length) {
      zngzFdzltj2NEW(diaoduzhilingtongji.value[0].name).then(res => {
        diaoduzhilingData.value = JSON.parse(res.data[0].value).data
        diaoduzhilingData.value.forEach((data, dataindex) => {
          data.dataIndex = dataindex
        })
      })
    }
  })

  zngzDdlbtjNEW().then(res => {
    console.log(res, '调度方式柱状图', JSON.parse(res.data[0].value).data)
    diaoduGetEcharts(JSON.parse(res.data[0].value).data)
  })
  caseInfoNEW().then(res => {
    console.log(res, '事件处置new', JSON.parse(res.data[0].value))
    caseInformationDATA.value = JSON.parse(res.data[0].value).data
  })

  yjcbdTable().then(res => {
    console.log(res, '应急物资的仓库')
    if (res.code === 200) {
      changkuvalue.value = res.rows
    }
  })
  // 积水点--全部
  // shortGet().then(res => {
  //   jishuiquanbu.value = JSON.parse(res.data[0].value).jsdxx
  //   rainMonitorTableData.value = JSON.parse(JSON.stringify(jishuiquanbu.value))
  // })
  jsdxxOnline().then(res => {
    jishuizaixian.value = JSON.parse(res.data[0].value).jsdxx
  })
  jsdxxOffline().then(res => {
    jishuilixian.value = JSON.parse(res.data[0].value).jsdxx
  })
  jsdxxRanking().then(res => {
    jishuipaihang.value = JSON.parse(res.data[0].value).jsdxx
  })

  bzswGET().then(res => {
    bengzhanshuiwei.value = JSON.parse(res.data[0].value).pump_station_water_level
  })

  gwyjGET().then(res => {
    gwyjDATA.value = JSON.parse(res.data[0].value).data
  })

  wzdptjGET().then(res => {
    wzdptjDATA.value = JSON.parse(res.data[0].value).data
  })

  bjxxGET().then(res => {
    bjxxDATA.value = JSON.parse(res.data[0].value).alarm_information
  })
  bjsbxxlbGET().then(res => {
    console.log(JSON.parse(res.data[0].value), 'JSON.parse(res.data[0].value)报警信息列表111')
    bjsbxxlbDATA.value = JSON.parse(res.data[0].value).alarm_device_information
  })

  ddzxgkGET().then(res => {
    ddzxgkDATA.value = JSON.parse(res.data[0].value).scheduling_execution
    setTimeout(() => {
      // diaoduGetEcharts(ddzxgkDATA.value)
    }, 10)
  })

  ztzlGET().then(res => {
    ztzlDATA.value = JSON.parse(res.data[0].value).data
  })

  // caseInfoGET().then(res => {
  //   caseInformationDATA.value = JSON.parse(res.data[0].value).data
  //   console.log(JSON.parse(res.data[0].value).data, '事件处置')
  // })

  dlqxGET().then(res => {
    console.log(JSON.parse(res.data[0].value), ' 短临气象')
    dlqxDATA.value = JSON.parse(res.data[0].value).data
    setTimeout(() => {
      getLeftFirstCharts(dlqxDATA.value)
    }, 0)
  })

  // 左侧上，第一个图表
  //
  nextTick(async () => {
    await getCameraListData()
    // echarts.init(bzyxtj1.value).setOption(bjtjData.value)
    // echarts.init(gwRef.value).setOption(bjtjData.value)
    // echarts.init(diaodufl1.value).setOption(bjtjData.value)
    // echarts.init(diaodufl2.value).setOption(bjtjData.value)
    // echarts.init(bzyxtj2.value).setOption(yxtjzhu.value)
    // echarts.init(bzyxtj3.value).setOption(yxtjzhu.value)
    // echarts.init(bzyxtj4.value).setOption(yxtjzhu.value)
  })
  initFlvPlayers()
})

const paishuiRowClick = row => {
  console.log(row, '弹窗-排水报警的某一行')
  fdapi.marker.focus(row.yldId, 1200, 0)
}

const yuntuClick = () => {
  emit('yuntuClickShow', 1)
}
const yuzhongkuaibao = () => {
  emit('yuntuClickShow', 2)
}

const wuziPageNum = ref(1)
const yuqingPageNum = ref(1)
const shijianPageNum = ref(1)
const wuziPageSize = ref(20)
const shijianPageSize = ref(20)
const yuqingPageSize = ref(20)
const wuziQuery = () => {
  yingjiwuziTable(
    formInlinewuzi.value.wzTypeName,
    formInlinewuzi.value.cbdId,
    wuziPageNum.value,
    wuziPageSize.value
  ).then(res => {
    console.log(res, '应急物资弹窗表格显示数据')
    if (res.code === 200) {
      wuziTotal.value = res.total
      wuzitableData.value = res.rows
    }
  })
}
const yingjiMoreClick = () => {
  wuziPageNum.value = 1
  wuzidialogVisible.value = !wuzidialogVisible.value
  wuziQuery()
}
const handleSizeChangewuzi = size => {
  console.log(size, '应急物资分页')
  wuziPageSize.value = size
  wuziPageNum.value = 1
  wuziQuery()
}
const handleCurrentChangewuzi = page => {
  console.log(page, '应急物资分页1')
  wuziPageNum.value = page
  wuziQuery()
}
const onSubmitwuzi = () => {
  wuziQuery()
}
const yuqingMore = () => {
  yuqingdialogVisible.value = true
  yuqingPageNum.value = 1
  yqxxgetPage().then(res => {
    console.log(res, '舆情分页')
    if (res.code === 200) {
      yuqingtableData.value = res.rows
    }
  })
}
const handleSizeChangeyuqing = size => {
  yuqingPageNum.value = 1
  yuqingPageSize.value = size
  yqxxgetPage(yuqingPageNum.value, yuqingPageSize.value).then(res => {
    console.log(res, '舆情分页')
    if (res.code === 200) {
      yuqingtableData.value = res.rows
    }
  })
}
const handleCurrentChangeyuqing = page => {
  yuqingPageNum.value = page
  yqxxgetPage(yuqingPageNum.value, yuqingPageSize.value).then(res => {
    console.log(res, '舆情分页')
    if (res.code === 200) {
      yuqingtableData.value = res.rows
    }
  })
}

const caseInformationGET = () => {
  shijiandialogVisible.value = true
  shijianPage()
}
const shijianPage = () => {
  caseInformationPage(
    shijianPageNum.value,
    shijianPageSize.value,
    formInlineshijian.value.eventGridName,
    formInlineshijian.value.rptTime
  ).then(res => {
    console.log(res, '事件分页')
    if (res.code === 200) {
      shijiantableData.value = res.rows
    }
  })
}
const onSubmitshijian = () => {
  console.log(formInlineshijian.value)

  shijianPageNum.value = 1
  shijianPage()
}
const handleSizeChangeshijian = size => {
  shijianPageSize.value = size
  shijianPageNum.value = 1
  shijianPage()
}
const handleCurrentChangeshijian = page => {
  shijianPageNum.value = page
  shijianPage()
}

const bengzhanItemClick = async row => {
  // const result = await getLocationsingle({ pageSize: 1000, sstype: row.type, sbname: row.name })
  // console.log(result, 'result')
  // 直接点击表格，没有点击上面的易涝点
  if (yilaodianditudata.value.length) {
    // 点击了易涝点
    fdapi.marker.focus(row.yldId, 1200, 0)
  } else {
    // 没有点击易涝点
    row.sbname = row.name
    emit('clickScene', { tagData: { currentSingleData: true }, locationData: [row] })
  }
  console.log(row, '单个泵站shuju---上面的易涝点已经联动了地图，点击这里，地图聚焦')
  // yilaodianditudata.value.forEach(item => {
  //   console.log(item.sbname, '哈哈')
  //   if (item.sbname === row.name) {
  //   }
  // })
  // row.sbname = row.name
  // emit('clickScene', { tagData: { currentSingleData: true }, locationData: [row] })
}

const fangxunFunction = () => {
  fkclxxbGET(
    fangxunPageNum.value,
    fangxunPageSize.value,
    formInlinefangxun.value.cphm,
    formInlinefangxun.value.jsrxm
  ).then(res => {
    console.log(res, '防汛车辆联动地图')
    // if (res.code === 200) {
    //   res.rows.forEach(item => {
    //     item.xcoordinate = item.jd
    //     item.ycoordinate = item.wd
    //     item.category = ''
    //   })
    //   res.rows.forEach(item => {
    //     item.sbname = item.cphm
    //   })
    // }
    // emit('tagClicked', { tagData: {}, locationData: res.rows })
    fangxundialogVisible.value = true
    res.rows.forEach(resData => {
      resData.newDhhm = resData.jsrlxdh.slice(0, 3) + '****' + resData.jsrlxdh.slice(resData.jsrlxdh.length - 4)
    })
    fangxuntableData.value = res.rows
    fangxunTotal.value = res.total
    emit('tagClickedbranch', { tagData: fxnlDATA.value, locationData: res.rows })
  })
}
const handleClickfangxuancheliang = item => {
  console.log(item, fdapi, '点击单个车辆')
  fdapi.marker.focus('FXCL_' + item.sbbh, 1200, 0)
}
const fangxuncheliang = () => {
  yilaodianditudata.value = []
  paishuisheshibaojingData.value = []
  fangxunFunction()
}
const fangxunrenyuanF = () => {
  yjdwGETperson(
    fangxunrenyuanPageNum.value,
    fangxunrenyuanPageSize.value,
    formInlinefangxunrenyuan.value.leader,
    formInlinefangxunrenyuan.value.leaderPhone
  ).then(res => {
    console.log(res, '点击防汛人员', res.rows)
    res.rows.forEach(resData => {
      resData.leaderPhones =
        resData.leaderPhone.slice(0, 3) + '****' + resData.leaderPhone.slice(resData.leaderPhone.length - 4)
    })
    fangxunrenyuantableData.value = res.rows
    fangxunrenyuanTotal.value = res.total
    fangxunrenyuandialogVisible.value = true
  })
}
const onSubmitfangxunrenyuan = () => {
  fangxunrenyuanF()
}
const fangxunrenyuanClick = () => {
  fangxunrenyuanF()
}
const rowClickTable = item => {
  console.log(item, '应急物资的单元行点击')
  item.eventGridName = item.ckname
  item.coordinate = item.xcoordinate + ',' + item.ycoordinate
  if (item.coordinate) {
    // emit('clickTable', { tableData: { coordinate: item.coordinate, device_name: '' } })
  }
  item.sbname = item.ckname
  item.taiyuanFlag = 'taiyuanFlag'
  emit('tagClicked', { tagData: item, locationData: [item] })
}

const bzlistGet = () => {
  bengzhandialogVisible.value = true
  bzxxbGET(bengzhanPageNum.value, bengzhanPageSize.value, formInlinebengzhan.value.name).then(res => {
    console.log(res, '泵站运行统计的更多数据')
    if (res.code === 200) {
      bengzhantableData.value = res.rows
      bengzhanTotal.value = res.total
    }
  })
}
const bengzhanClick = () => {
  bengzhanPageNum.value = 1
  bzlistGet()
}
const onSubmitbengzhan = () => {
  bengzhanPageNum.value = 1
  bzlistGet()
}
const handleSizeChangebengzhan = size => {
  bengzhanPageSize.value = size
  bengzhanPageNum.value = 1
  bzlistGet()
}
const handleSizeChangefangxun = size => {
  fangxunPageNum.value = 1
  fangxunPageSize.value = size
  fangxunFunction()
}
const handleSizeChangefangxunrenyuan = size => {
  fangxunrenyuanPageNum.value = 1
  fangxunrenyuanPageSize.value = size
  fangxunrenyuanF()
}
const handleCurrentChangefangxunrenyuan = page => {
  fangxunrenyuanPageNum.value = page
  fangxunrenyuanF()
}
const handleCurrentChangebengzhan = page => {
  bengzhanPageNum.value = page
  bzlistGet()
}
const handleCurrentChangefangxun = page => {
  fangxunPageNum.value = page
  fangxunFunction()
}
const wuItemClick = item => {
  yilaodianditudata.value = []
  paishuisheshibaojingData.value = []
  console.log(item, '物资的单个点击')
  wzwzlxGET(item.type).then(res => {
    console.log(res, '物资的单个点击数据返回')
    res.rows.forEach(item => {
      ;((item.sbname = item.ckname), (item.taiyuanFlag = 'taiyuanFlag'))
    })
    emit('tagClicked', { tagData: item, locationData: res.rows })
  })
}

const onSubmitfangxun = () => {
  fangxunPageNum.value = 1
  fangxunFunction()
}
</script>

<style lang="scss" scoped>
.new-add-border {
  background: linear-gradient(155deg, rgba(0, 102, 255, 0.1) 0%, rgba(0, 102, 255, 0.1) 30%, rgba(0, 155, 255, 0) 100%);
  border: 2px solid rgba(64, 228, 251, 0.3);
  position: relative;
}
.leftbei {
  position: absolute;
  left: 0;
  width: 7px;
  height: 197px;
  top: 30%;
  background: url('@/assets/images/intellisense/leftbei.png') no-repeat center/100%;
}
.righttbei {
  position: absolute;
  right: 0;
  width: 7px;
  height: 197px;
  top: 30%;
  background: url('@/assets/images/intellisense/rightbeibg.png') no-repeat center/100%;
}
.bottombeibg {
  position: absolute;
  bottom: 0;
  width: 460px;
  height: 6px;
  left: 30%;
  background: url('@/assets/images/intellisense/bottombeibg.png') no-repeat center/100%;
  &.bottom-left {
    left: 21%;
  }
}

.neilao-small {
  width: 342px;
  height: vh(50);
  background: url('@/assets/images/intellisense/neilaoxiao.png') no-repeat center/100%;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 400;
  font-size: 23px;
  color: #ffffff;
  line-height: 33px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding-top: 8px;
  padding-left: 30px;
  margin-left: 10px;
}
.chart-headers {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: vh(46);
  // margin-bottom: vh(5);
  background: url('@/assets/images/home/<USER>') repeat;
  background-size: 100% 100%;
  padding: 0;
  &.touchang {
    background: url('@/assets/images/intellisense/touchang.png') repeat;
  }
  &.touzhong {
    background: url('@/assets/images/intellisense/touzhong.png') repeat;
  }
  &.touduan {
    background: url('@/assets/images/intellisense/touduan.png') repeat;
  }
  .title-more {
    font-family:
      Source Han Sans,
      Source Han Sans;
    font-weight: 400;
    font-size: 20px;
    color: #d8f1ff;
    line-height: 29px;
    text-align: right;
    font-style: normal;
    text-transform: none;
    padding-right: 10px;
    cursor: pointer;
  }
  .chart-title {
    font-weight: normal;
    font-size: vh(28);
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 vw(50);
    font-family: JiangChengXieHei;
    color: #d8f1ff;
    .baojing-value {
      font-family:
        Source Han Sans,
        Source Han Sans;
      font-weight: 350;
      font-size: 26px;
      color: #fff;
      line-height: 35px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-left: 20px;
    }
    .title-value {
      margin-left: 16px;
      margin-top: 10px;
      font-family:
        Source Han Sans,
        Source Han Sans;
      font-weight: 350;
      font-size: 24px;
      color: #d8f1ff;
      line-height: 35px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    // @include respond-to("ultra-wide") {
    //   font-size: 18px;
    // }
  }

  .alarm-tabs {
    display: flex;
    height: 100%;
  }

  .alarm-tab {
    padding: 0 vw(20);
    height: 100%;
    display: flex;
    align-items: center;
    color: #fff;
    font-family: JiangChengXieHei, JiangChengXieHei;
    font-weight: normal;
    font-size: vh(20);
    cursor: pointer;
    margin-left: vw(5);
    background: url('@/assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;

    &.active {
      background: url('@/assets/images/home/<USER>') no-repeat;
      background-size: 100% 100%;
    }
  }
}

.content-left-wrap {
  display: flex;
  // height: 100%;
  height: calc(100% - vh(180));
  .wrap-left {
    width: 1284px;
    // height: vh(1000);

    .paishuixitong {
      // width: 1184px;
      // height: 244px;
      // margin-bottom: 18px;
      padding-bottom: 12px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      .p-s-x-t-w {
        height: 242px;
        position: relative;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-around;
        // gap: 20px;
        // padding-top: 10px;
        .ps-x-tw-l {
          display: flex;
          flex: 1;
        }
        .ps-x-tw-l-r {
          display: flex;
          flex: 1;
          justify-content: center;
          align-items: center;
          padding-left: 60px;
          .ps-x-tw-circle {
            position: relative;
            width: 0;
            height: 0;
            padding-bottom: 24%;
            padding-left: 24%;
            border: 23px solid rgba(250, 220, 88);
            border-radius: 50%;
            position: relative;
            background: transparent;
            transition: all 0.5s;
            cursor: pointer;
            &:hover {
              transform: scale(1.1);
            }
            .ps-x-tw-circle-li {
              position: absolute;
              top: 80px;
              left: -62px;
              width: 40px;
              height: 1px;
              background: #fff;
              .ps-x-tw-circle-li-d {
                position: absolute;
                top: 5px;
                left: -18px;
                width: 20px;
                height: 1.5px;
                background: #fff;
                transform: rotate(-30deg);
              }
              .ps-x-tw-circle-li-c {
                position: absolute;
                top: 14px;
                left: -110px;
                min-width: 150px;
                height: 20px;
                font-size: 22px;
                color: #fff;
                display: flex;
                align-items: center;
              }
            }
          }
          .ps-x-tw-circle1 {
            position: relative;
            width: 0;
            height: 0;
            padding-bottom: 24%;
            padding-left: 24%;
            border: 23px solid rgba(84, 122, 198);
            border-radius: 50%;
            position: relative;
            background: transparent;
            transition: all 0.5s;
            cursor: pointer;
            .ps-x-tw-circle1-value {
              position: absolute;
              top: 60px;
              left: 10px;
              color: #fff;
              font-size: 22px;
            }
            &:hover {
              transform: scale(1.1);
            }
            .ps-x-tw-circle-li {
              position: absolute;
              top: 80px;
              right: -62px;
              width: 40px;
              height: 1px;
              background: #fff;
              .ps-x-tw-circle-li-d {
                position: absolute;
                top: 5px;
                right: -18px;
                width: 20px;
                height: 1.5px;
                background: #fff;
                transform: rotate(30deg);
              }
              .ps-x-tw-circle-li-c {
                position: absolute;
                top: 14px;
                right: -130px;
                min-width: 150px;
                height: 20px;
                font-size: 22px;
                color: #fff;
              }
            }
          }
        }
        .x-t-item {
          cursor: pointer;
          width: 183px;
          height: 130px;
          display: flex;
          // padding: 0 10px;
          justify-content: center;
          align-items: center;
          margin-right: 24px;
          &:nth-child(3) {
            margin-right: 0;
          }
          .item-i {
            width: 48px;
            height: 48px;
            margin-right: 10px;
            img {
              display: block;
              width: 100%;
            }
            // background: url('@/assets/images/intellisensenew/changjing1.png') no-repeat center/100%;
          }
          .i-i-r-name {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 24px;
            color: #fff;
            line-height: 32px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            width: 106px;
          }
          .i-i-r-n-v {
            display: flex;
            align-items: center;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 700;
            font-size: 24px;
            color: #ffffff;
            line-height: 35px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .on-line {
              color: orange;
            }
            .to-tal {
              color: #fff;
            }
            .warn-ing {
              color: red;
            }
          }
        }
      }
    }
    .bz-yx {
    }
    .bz-yx-b {
      // width: 1184px;
      // height: 480px;
      display: flex;
      .bz-yx-i {
        .bz-yx-ww {
          display: flex;
          width: 936px;
        }
        .yx-b-left {
          flex: 1;
          position: relative;
          .duibi {
            position: absolute;
            top: 10px;
          }
          .yubaojishui {
            top: 210px;
            position: absolute;
          }
        }
        .yx-b-middle {
          flex: 1;
          position: relative;
          .duibi {
            position: absolute;
            top: 10px;
          }
          .yubaojishui {
            top: 210px;
            position: absolute;
          }
        }
      }

      > div {
        flex: 1;
        .rain-monitor-table-container-tj {
          // width: 100%;
          //   height: calc(100% - vh(45));
          height: 420px;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          // margin-top: 27px;

          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            align-items: center;
            min-height: vh(40);
            background: #0d4873;
            border: 1px solid;
            border-image: linear-gradient(
                90deg,
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
              )
              1 1;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            // line-height: 38px;
            text-align: center;
            font-style: normal;
            text-transform: none;
            // color: #fff;
            // font-weight: bold;
            // font-size: vh(22);
            // border-bottom: vh(2) solid #205080;
            // font-family: Alibaba-PuHuiTi, sans-serif;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            // overflow: hidden;
            white-space: wrap;
            text-overflow: ellipsis;
          }
          .th-index1 {
            width: 38%;
          }
          .th-station1 {
            width: 25%;
          }
          .th-hour1 {
            width: 21%;
          }
          .th-warning1 {
            width: 20%;
          }

          .rain-monitor-table-body {
            // height: calc(100% - vh(48));
            height: 380px;
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            cursor: pointer;
            display: flex;
            align-items: center;
            min-height: vh(54);
            padding: 14px 0;
            font-size: 24px;
            color: #fff;
            border-bottom: vh(2) solid rgba(216, 216, 216, 0.2);
            transition: background 0.2s;
            &:nth-child(1) {
              // height: 64px;
            }
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
        .yx-pie {
          height: 200px;
        }
        .yx-zhu {
          height: 290px;
        }
        .yx-m-pie {
          height: 220px;
        }
        .yx-m-zhu {
          height: 270px;
        }
      }
      .yx-b-right {
        width: 700px;
      }
    }
    .duanlin {
      height: 390px;
      // width: 1184px;
      margin-bottom: 20px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
    }
    .wrap-left-charts {
      height: vh(350);
      width: 100%;
      // border: 1px solid yellow;
    }
    .jishui {
      margin-top: 29px;
      display: flex;
      justify-content: space-between;
      .jishui-left {
        // width: 59%;
        .jishui-wrap {
          width: 1006px;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          .rain-monitor-table-container {
            width: 100%;
            //   height: calc(100% - vh(45));
            height: 290px;
            margin-top: 27px;

            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              min-height: vh(40);
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 500;
              font-size: 22px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
              // color: #fff;
              // font-weight: bold;
              // font-size: vh(22);
              // border-bottom: vh(2) solid #205080;
              // font-family: Alibaba-PuHuiTi, sans-serif;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .th-index1 {
              width: 20%;
            }
            .th-station1 {
              width: 20%;
            }
            .th-hour1 {
              width: 20%;
            }
            .th-warning1 {
              width: 40%;
            }

            .rain-monitor-table-body {
              // height: calc(100% - vh(48));
              height: 250px;
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              display: flex;
              align-items: center;
              min-height: vh(54);
              font-size: 22px;
              color: #fff;
              border-bottom: vh(2) solid rgba(216, 216, 216, 0.2);
              transition: background 0.2s;
              &:nth-child(1) {
                height: 64px;
              }
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(18);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }
        }

        .bengzhan-wrap {
          margin-top: 29px;
          width: 1006px;
          .rain-monitor-table-container1 {
            width: 100%;
            margin-top: 27px;
            //   height: calc(100% - vh(45));

            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              min-height: vh(40);
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 500;
              font-size: 22px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              // overflow: hidden;
              // white-space: nowrap;
              text-overflow: ellipsis;
            }
            .th-index1 {
              width: 18%;
            }
            .th-station1 {
              width: 12%;
            }
            .th-hour1 {
              width: 20%;
            }
            .th-warning1 {
              width: 19%;
            }
            .th-warning2 {
              width: 14%;
            }
            .th-warning3 {
              width: 14%;
            }

            .rain-monitor-table-body {
              height: 250px;
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              display: flex;
              align-items: center;
              min-height: vh(54);
              font-size: vh(22);
              color: #fff;
              border-bottom: vh(2) solid rgba(216, 216, 216, 0.2);
              transition: background 0.2s;
              &:nth-child(1) {
                height: 64px;
              }
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(18);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }
        }
      }
      .jishui-right {
        // width: 39%;
        .guanwang-container {
          width: 653px;
          height: vh(360);
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        }
        .guanwang {
          display: flex;
          height: 292px;
          .guanwang-wrap {
            height: 100%;
            flex: 1;
            color: #fff;
            font-size: 22px;
            margin-top: 28px;
            .guanwang-item {
              height: vh(190);
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              padding-bottom: 90px;
              .guanwang-item-val {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 350;
                font-size: 26px;
                color: #ffffff;
                line-height: 38px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
              &.active0 {
                background: url('@/assets/images/intellisense/guanwangjujing1.png') no-repeat center/100%;
                .guanwang-item-value {
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 700;
                  font-size: 52px;
                  line-height: 75px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  background: linear-gradient(90deg, #ffffff 0%, #76c8fc 100%);
                  background-clip: text;
                  color: transparent;
                }
              }
              &.active1 {
                background: url('@/assets/images/intellisense/guanwangjujing2.png') no-repeat center/100%;
                .guanwang-item-value {
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 700;
                  font-size: 52px;
                  line-height: 75px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  background: linear-gradient(90deg, #ffffff 0%, #ff6a00 100%);
                  background-clip: text;
                  color: transparent;
                }
              }
              &.active2 {
                background: url('@/assets/images/intellisense/guanwangjujing3.png') no-repeat center/100%;
                .guanwang-item-value {
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 700;
                  font-size: 52px;
                  line-height: 75px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  background: linear-gradient(90deg, #ffffff 0%, #0a936b 100%);
                  background-clip: text;
                  color: transparent;
                }
              }
            }
            .guanwang-text {
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: 30px;
              color: #ffffff;
              line-height: 38px;
              text-align: left;
              font-style: normal;
              text-transform: none;
              text-align: center;
              margin-top: 14px;
            }
          }
        }

        .wuzi-diaopei {
          height: 374px;
          .rain-monitor-table-container-wuzi {
            width: 100%;
            margin-top: 28px;
            //   height: calc(100% - vh(45));

            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              height: 40px;
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 500;
              font-size: 22px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .th-index1 {
              width: 20%;
            }
            .th-station1 {
              width: 20%;
            }
            .th-hour1 {
              width: 20%;
            }
            .th-warning1 {
              width: 40%;
            }

            .rain-monitor-table-body {
              // height: calc(100% - vh(48));
              height: 264px;
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: 22px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              display: flex;
              align-items: center;
              min-height: vh(54);
              color: #fff;
              border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
              transition: background 0.2s;
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(22);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }
        }
      }
    }
  }
  .wrap-right {
    // width: 33.3%;
    // height: vh(400);
    margin-left: 22px;
    width: vw(1200);
    .wrap-right-con {
      // width: 1184px;
      // height: 244px;
      // margin-bottom: 12px;
      .p-s-x-t-w {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-around;
        // gap: 20px;
        // padding-top: 10px

        .x-t-item {
          cursor: pointer;
          width: 19.66%;
          height: 130px;
          display: flex;
          // padding: 0 10px;

          justify-content: center;
          align-items: center;
          .item-i {
            width: 48px;
            height: 48px;
            margin-right: 14px;
            img {
              display: block;
              width: 100%;
            }
            // background: url('@/assets/images/intellisensenew/changjing1.png') no-repeat center/100%;
          }

          .i-i-r-name {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 22px;
            color: #fff;
            line-height: 32px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .i-i-r-n-v {
            display: flex;
            align-items: center;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 700;
            font-size: 24px;
            color: #ffffff;
            line-height: 35px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .on-line {
              color: orange;
            }
            .to-tal {
              color: #fff;
            }
            .warn-ing {
              color: red;
            }
          }
        }
      }
    }
    .baojingxinxi {
      // height: 260px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      .guanwang {
        display: flex;
        height: 245px;
        padding-top: 24px;

        .guanwang-wrap {
          flex: 1;
          color: #fff;
          font-size: 22px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          padding-bottom: 26px;
          &:nth-child(1) {
            background: url('@/assets/images/intellisense/baojingxinxi1.png') no-repeat center/90%;
            .guanwang-item .guanwang-item-value {
              background: linear-gradient(90deg, #ffffff 0%, #a9ca24 100%);
              background-clip: text;
              color: transparent;
            }
          }
          &:nth-child(2) {
            background: url('@/assets/images/intellisense/baojingxinxi2.png') no-repeat center/90%;
            .guanwang-item .guanwang-item-value {
              background: linear-gradient(90deg, #ffffff 0%, #59b8f6 100%);
              background-clip: text;
              color: transparent;
            }
          }
          &:nth-child(3) {
            background: url('@/assets/images/intellisense/baojingxinxi3.png') no-repeat center/90%;
            .guanwang-item .guanwang-item-value {
              background: linear-gradient(90deg, #ffffff 0%, #358e9e 100%);
              background-clip: text;
              color: transparent;
            }
          }
          .guanwang-item {
            // height: 100px;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 700;
            font-size: 38px;
            line-height: 55px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .guanwang-text {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 22px;
            color: #ffffff;
            line-height: 26px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .shuiweixinxi {
        display: flex;
        justify-content: space-around;
        .swxx {
          width: 20%;
          height: 120px;
          // background: #0d4873;

          display: flex;
          flex-direction: column;
          justify-content: center;
          align-content: center;

          .swxx-up {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 900;
            font-size: 34px;
            line-height: 39px;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
            background-clip: text;
            color: transparent;
            text-align: center;
          }
          .swxx-down {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 28px;
            color: #ffffff;
            font-style: normal;
            text-transform: none;
            text-align: center;
          }
        }
      }
    }

    .baojingshebeixinxi {
      height: vh(240);
      // margin-top: 20px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      .rain-monitor-table-container-xinxi {
        width: 100%;
        //   height: calc(100% - vh(45));

        display: flex;
        // padding: 0 6px;
        flex-direction: column;
        box-sizing: border-box;
        overflow: hidden;
        .rain-monitor-table-header {
          display: flex;
          height: vh(40);
          // margin-top: vh(25);
          background: #0d4873;
          border: 1px solid;
          border-image: linear-gradient(
              90deg,
              rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
              rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
              rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
            )
            1 1;
          font-family:
            Source Han Sans,
            Source Han Sans;
          font-weight: 500;
          font-size: 26px;
          color: #ffffff;
          line-height: 38px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
        .rain-th,
        .rain-td {
          text-align: center;
          padding: 0;
          margin: 0;
          box-sizing: border-box;
          // overflow: hidden;
          white-space: wrap;
          // text-overflow: ellipsis;
        }
        .th-index1 {
          width: 40%;
        }
        .th-station1 {
          width: 20%;
        }
        .th-hour1 {
          width: 20%;
        }
        .th-warning1 {
          width: 20%;
        }

        .rain-monitor-table-body {
          height: vh(200);
          overflow-y: auto;
          -ms-overflow-style: none; /* IE 和 Edge */
          scrollbar-width: none; /* Firefox */
          padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
        }
        .rain-monitor-table-body::-webkit-scrollbar {
          display: none; /* Chrome, Safari 和 Opera */
        }
        .rain-tr {
          cursor: pointer;
          display: flex;
          align-items: center;
          min-height: vh(54);
          font-family:
            Source Han Sans,
            Source Han Sans;
          font-weight: 350;
          font-size: 24px;
          color: #ffffff;
          // line-height: 60px;
          text-align: center;
          font-style: normal;
          padding: 8px 0;
          text-transform: none;
          border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
          transition: background 0.2s;
        }
        .rain-tr:hover {
          background: rgba(0, 198, 255, 0.1);
        }
        .over-red {
          color: #ff3b3b;
          font-weight: bold;
        }
        .over-blue {
          color: #1a86fc;
          font-weight: bold;
        }
        .level-badge {
          display: inline-block;
          min-width: vw(28);
          padding: vh(2) vw(8);
          border-radius: vh(12);
          font-weight: bold;
          font-size: vh(18);
        }
        .level-red {
          color: #ff3b3b;
        }
        .level-blue {
          color: #1a86fc;
        }
        .level-light {
          color: #7ed6fc;
        }
      }
    }
    .bz-yx-tj {
      margin-top: 20px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
      .bz-yx-bnew {
        display: flex;
        height: 240px;
        margin-top: 16px;

        .bz-yx-pie {
          width: 34%;
          height: 100%;
        }
        .rain-monitor-table-container-new-bz {
          width: 100%;
          //   height: calc(100% - vh(45));

          display: flex;
          // padding: 0 6px;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            min-height: vh(40);
            // margin-top: vh(25);
            // background: #0d4873;
            border: 1px solid;
            border-image: linear-gradient(
                90deg,
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
              )
              1 1;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 26px;
            color: #ffffff;
            // line-height: 38px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            // overflow: hidden;
            white-space: wrap;
            // text-overflow: ellipsis;
          }
          .th-index1 {
            width: 22%;
          }
          .th-station1 {
            width: 20%;
          }
          .th-hour1 {
            width: 20%;
          }
          .th-warning1 {
            width: 20%;
          }

          .rain-monitor-table-body {
            height: vh(200);
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            cursor: pointer;
            display: flex;
            align-items: center;
            min-height: vh(54);
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 24px;
            color: #ffffff;
            // line-height: 60px;
            text-align: center;
            font-style: normal;
            padding: 8px 0;
            text-transform: none;
            border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
            transition: background 0.2s;
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
      }
    }

    .left-r-yingji {
      display: flex;
      padding-top: 18px;
      justify-content: space-between;
      .yftop {
        width: 50%;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      }
      .p-s-x-t-w-r {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding-right: 10px;
        // padding-left: 20px;
        // gap: 1px;
        // background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        .x-t-item {
          cursor: pointer;
          width: 190px;
          // border: 1px solid red;
          min-height: 120px;
          padding-top: 12px;
          // display: flex;
          align-items: center;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          padding-top: 20px;
          &:nth-child(1),
          &:nth-child(4),
          &:nth-child(7) {
            padding-left: 12px;
          }
          // padding-left: 20px;
          .x-t-item-wrap {
            display: flex;
            justify-content: flex-start;
            height: 100%;
            img {
              display: block;
              width: 40px;
              height: 40px;
              margin-right: 12px;
            }
          }

          &:last-child {
            padding-left: 0;
          }
          .i-i-right {
            display: flex;
            align-items: baseline;
            flex-wrap: wrap;
          }
          .item-i {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 25px;
            color: #ffffff;
            line-height: 32px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .i-i-r-name {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 700;
            font-size: 26px;
            line-height: 41px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #0098fa 100%);
            background-clip: text;
            color: transparent;
          }
          .i-i-r-n-v {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 22px;
            color: #ffffff;
            // line-height: 29px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 8px;
          }
        }
      }
      .shebeibaojing {
        width: 49%;
        height: 390px;
        // padding: 20px 0 0 0;
        min-height: 410px;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
        .sbbj-i {
          height: 344px;
          padding-left: 20px;
          padding-top: 10px;
          .sbbj-i-i {
            display: flex;
            height: 110px;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            .sbbj-left {
              margin-right: 40px;
              width: 150px;
              height: 90px;
              background: url('@/assets/images/intellisensenew/shebeibaojing.png') no-repeat center/100%;
              .sbbj-p {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 700;
                font-size: 44px;
                color: #ffffff;
                line-height: 52px;
                text-align: center;
                font-style: normal;
                text-transform: none;
              }
              .sbbj-v {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 400;
                font-size: 26px;
                color: #ffffff;
                line-height: 29px;
                text-align: center;
                font-style: normal;
                text-transform: none;
                margin-top: 0px;
              }
            }
            .sbbj-right {
              cursor: pointer;
              flex: 1;
            }

            .sbbjup {
              display: flex;
              align-items: center;
              .sbbj-up-t {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 700;
                font-size: 26px;
                color: #ffffff;
                line-height: 32px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
              .sbbj-up-1 {
                margin: 0 4px 0 12px;
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 900;
                font-size: 28px;
                line-height: 39px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
                background-clip: text;
                color: transparent;
                margin-bottom: 2px;
              }
              .sbbj-up-p {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 900;
                font-size: 28px;
                line-height: 39px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
                background-clip: text;
                color: transparent;
              }
            }

            .sbbjdown {
              display: flex;
              .sbbjdownxia {
                display: flex;
                .sbbjdownt {
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 350;
                  font-size: 22px;
                  color: #fff;
                  line-height: 32px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  margin-right: 15px;
                }
                .sbbjdownp {
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 700;
                  font-size: 24px;
                  color: #ffffff;
                  line-height: 32px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                }
              }
              .sbbjline {
                width: 2px;
                height: 20px;
                border-left: 1px solid #676767;
                margin: 0 30px;
                margin-top: 7px;
              }
            }
          }
        }
      }
    }
  }
}

// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

.bg-layer-3 {
  // background-image: url('@/assets/images/home/<USER>');
  z-index: 3;
}

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: auto;
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.left-container {
  left: 0;
  // width: 35%;
  // background: linear-gradient( 90deg, rgba(0,0,0,0) 0%, rgba(19,44,57,0.7745) 15%, rgba(19,44,57,0.35) 100%);
  // background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.77) 26%, rgba(19, 44, 57, 0.95) 100%);
  .left-section {
    width: 100%;
    height: 100%;
    padding: vh(40) vw(0) vh(20) vw(100);
    pointer-events: auto;
    // background: linear-gradient( 90deg, rgba(0,0,0,0) 0%, rgba(19,44,57,0.7745) 15%, rgba(19,44,57,0.95) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .time-weather {
    display: flex;
    align-items: center;
    color: #fff;
    padding-left: vw(40);
    background: blue;
    .time-date {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .time {
      font-size: vh(24);
    }

    .date {
      font-size: vh(14);
      margin-right: vw(2);
    }

    .divider {
      margin: 0 vw(20);
      width: vw(1);
      height: vh(30);
      background-color: #3a607c;
    }

    .weather {
      display: flex;
      align-items: center;
    }

    .weather-icon {
      width: vw(24);
      height: vh(24);
      margin-right: vw(10);
    }

    .weather-icon1 {
      width: vw(24);
      height: vh(22);
    }

    .temperature {
      font-size: vh(24);
    }
  }

  .left-content {
    // background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.77) 26%, rgba(19, 44, 57, 0.95) 100%);
    width: 100%;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.7745) 15%, rgba(19, 44, 57, 0.35) 100%);
    height: calc(100% - 10px);
    display: flex;
    flex-direction: column;
    margin-top: vh(10);
    gap: vh(20);

    .content-layout-first {
      flex: 1;
      width: 100%;
      // background: rgba(0, 0, 0, 0.2);
      .content-layout-header {
        width: 100%;
        height: vh(120);
        background-image: url('@/assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 vw(95);
        margin-bottom: 20px;

        .weather-info-bar {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          color: #fff;
          padding: 0 vw(15);
        }

        .current-weather {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 100%;
          gap: vw(48);
        }

        .weather-label {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          line-height: vh(33);
        }

        .city-info {
          display: flex;
          align-items: center;
        }

        .location-icon {
          display: inline-block;
          width: vw(22);
          height: vh(24);
          margin-right: vw(20);
        }

        .city-name {
          font-weight: normal;
          font-size: vh(32);
          color: #ffffff;
          margin-top: vh(-8);
        }

        .weather-detail {
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          justify-content: center;
          height: 100%;
          gap: vw(15);
          min-width: 400px;
          font-size: 23px;
        }

        .weather-icon-large {
          display: flex;
          align-items: center;
        }

        .weather-icon-large img {
          width: vw(24);
          height: vh(24);
          margin-right: vw(10);
          object-fit: contain;
        }

        .weather-data {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .weather-type {
          display: flex;
          align-items: center;
          gap: vw(10);
        }

        .weather-name {
          font-weight: normal;
          font-size: vh(30);
          color: #ffffff;
          line-height: vh(42);
          text-align: center;
          font-style: normal;
        }

        .weather-temp {
          font-weight: normal;
          font-size: vh(22);
          color: #ffffff;
          line-height: vh(30);
          font-style: normal;
          text-align: center;
          width: vw(72);
          height: vh(31);
          background: #1ecfa5;
          border-radius: vw(16);
        }

        .wind-info {
          display: flex;
          align-items: center;
          gap: vw(20);
          font-weight: normal;
          font-size: vh(27);
          color: #ffffff;
          text-align: left;
          font-style: normal;
          margin-top: vh(3);
        }

        .weather-forecast {
          display: flex;
          height: 100%;
          align-items: center;
        }

        .forecast-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 vw(30);
        }

        .forecast-title {
          font-weight: normal;
          font-size: vh(20);
          color: #ffffff;
          text-align: left;
          font-style: normal;
        }

        .forecast-value {
          .forecast-value-num {
            font-weight: bold;
            font-size: vh(33);
            color: #24ceb8;
            font-style: normal;
          }

          .forecast-value-unit {
            font-weight: normal;
            font-size: vh(24);
            color: #ffffff;
            font-style: normal;
          }
        }

        .forecast-value1 {
          .forecast-value-num {
            font-weight: bold;
            font-size: vh(24);
            color: #92d3ec;
            font-style: normal;
          }

          .forecast-value-unit {
            font-weight: normal;
            font-size: vh(24);
            color: #ffffff;
            font-style: normal;
          }
        }

        .publish-info {
          width: vw(225);
          height: vh(77);
          border-radius: vw(1);
          display: flex;
          justify-content: space-around;
          align-items: center;
          gap: vw(20);
        }

        .publish-label {
          display: flex;
          justify-content: center;
          align-items: center;
          // width: 40%;
          height: 100%;
          font-weight: normal;
          font-size: vh(25);
          padding: 0 12px;
          color: #ffffff;
          font-style: normal;
          background: rgba(110, 204, 255, 0.04);
          cursor: pointer;
        }

        .publish-times {
          font-weight: normal;
          font-size: vh(22);
          color: #ffffff;
          font-style: normal;
        }

        .time-item {
          font-weight: normal;
          font-size: vh(22);
          color: #ffffff;
          font-style: normal;
        }

        .divider-line {
          width: vw(1);
          height: vh(70);
        }
      }
    }
  }
}

.middle-container {
  left: 46%;
  height: auto;
  .middle-section {
    /* 中间区域标题 */
    .section-title {
      font-family: YouSheBiaoTiHei;
      font-size: vh(80);
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }

    /* 导航按钮 */
    .nav-buttons {
      display: flex;
      justify-content: center;
      gap: vw(20);
      margin-top: vh(60);
    }

    .nav-button {
      width: vw(200);
      height: vh(80);
      background-image: url('@/assets/images/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      padding-top: vh(14);
      font-family: JiangChengXieHei;
      color: #fff;
      font-size: vh(24);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .nav-button.active {
      background-image: url('@/assets/images/home/<USER>');
      color: #fff;
      font-weight: bold;
    }
  }
}

.right-container {
  right: 0;
  // width: 30%;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.77) 26%, rgba(19, 44, 57, 0.95) 100%);
  .right-section {
    width: 100%;
    height: 100%;
    padding: vh(40) vw(100) vh(20) vw(0);
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .user-portal-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: vh(10) vw(20);
    gap: vw(30);
    border-radius: vh(4);
    background: rgba(6, 72, 146, 0.24); // 添加背景色

    .user-info,
    .portal-back {
      display: flex;
      align-items: center;
      gap: vw(10);

      img {
        width: vh(40);
        height: vh(40);
        border-radius: 50%;
        object-fit: cover;
      }

      span {
        color: #fff;
        font-size: vh(16);
      }
    }
  }

  .right-content {
    // padding-top: vh(140);
    // background: rgba(255, 255, 255, 0.1);
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.77) 26%, rgba(19, 44, 57, 0.95) 100%);
    border-radius: vh(4);
    width: 100%;
    height: calc(100% - 15px);
    display: flex;
    gap: vw(20);
    .right-video {
      width: 900px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      .video-contents {
        padding-top: 32px;
        padding-left: 18px;
        padding-right: 18px;
        .video-change {
          display: flex;
          height: 50px;
          // background: rgba(37, 148, 228, 0.1);
          background: #1b425e;
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #2594e4;
          > div {
            transition: all 0.3s ease;
            cursor: pointer;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #2594e4;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 18px;
            color: #ffffff;
            line-height: 32px;
            text-align: center;
            font-style: normal;
            text-transform: none;
            &:last-child {
              border-right: none;
            }
            &.videoActive {
              background: #2594e4;
              border-radius: 2px 2px 2px 2px;
            }
          }
        }
        .video-bofang {
          display: flex;
          flex-wrap: wrap;
          gap: vh(30);
          margin-top: 30px;
          padding-left: 8px;
          .video-bofang-wrap {
            margin-bottom: 22px;
            width: 48%;
            .bofang {
              width: 100%;
              height: 260px;
              border-radius: 0px 0px 0px 0px;
              border: 1px solid #108dd5;
              padding: 2px;
            }
            .bf-addr {
              font-size: 24px;
              color: #fff;
              padding: 12px 0;
              font-weight: 600;
            }
            .jiang-text {
              font-size: 22px;
              color: #fff;
            }
          }
        }
      }
    }
    .video-besides {
      width: vw(1710);
      .diaodu-zl-tj {
        min-height: 440px;
        margin-bottom: 22px;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
        .diaoduzhixingtop {
          flex: 1;
          // background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.156) 100%);
          margin-bottom: 20px;
          .charts-diaodu-f {
            flex: 1;
            height: vh(310);
          }
        }
        .diaodu-fl-w {
          display: flex;
          .charts-diaodu-fl {
            flex: 1;
            height: vh(310);
          }
        }
        .c-r-t-cc {
          display: flex;
          align-items: center;
          justify-content: space-around;
          margin: 18px 0 18px 0;
          padding-top: 24px;
          min-height: 74px;
          .c-r-t-cc-i {
            cursor: pointer;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 216px;
            height: 50px;
            background: url('@/assets/images/intellisense/tongji2.png') no-repeat center/100%;
            &.cc-i-active {
              background: url('@/assets/images/intellisense/tongji1.png') no-repeat center/100%;
            }
            .c-r-t-cc-t,
            .c-r-t-cc-t-title {
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 700;
              font-size: 24px;
              color: #ffffff;
              line-height: 26px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .c-r-t-cc-t {
              position: absolute;
              top: -20px;
              left: 0;
              width: 100%;
            }
            .c-r-t-cc-bb {
              position: relative;
              bottom: 4px;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 900;
              font-size: 28px;
              text-align: left;
              font-style: normal;
              text-transform: none;
              background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
              background-clip: text;
              color: transparent;
              padding-top: 16px;
            }
          }
        }
        .rain-monitor-table-container-zhiling {
          width: 100%;
          //   height: calc(100% - vh(45));

          display: flex;
          // padding: 0 6px;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            height: vh(40);
            // margin-top: vh(25);
            background: #0d4873;
            border: 1px solid;
            border-image: linear-gradient(
                90deg,
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
              )
              1 1;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 26px;
            color: #ffffff;
            line-height: 38px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            // overflow: hidden;
            white-space: wrap;
            // text-overflow: ellipsis;
          }
          .th-index1 {
            width: 22%;
          }
          .th-station1 {
            width: 20%;
          }
          .th-hour1 {
            width: 20%;
          }
          .th-warning1 {
            width: 20%;
          }

          .rain-monitor-table-body {
            height: vh(240);
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            cursor: pointer;
            display: flex;
            align-items: center;
            min-height: vh(54);
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 24px;
            color: #ffffff;
            // line-height: 60px;
            text-align: center;
            font-style: normal;
            padding: 12px 0;
            text-transform: none;
            border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
            transition: background 0.2s;
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
      }
      .right-gather {
        display: flex;
        justify-content: space-between;
        height: auto;
        padding-bottom: 12px;
        gap: 20px;
        // margin-top: 20px;
        .diaoduzhixing {
          // width: 912px;
          flex: 1;
          // height: vh(610);

          .diaoduzhixingtop {
            // background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
            margin-bottom: 20px;
          }
          .diaodubottom {
            // margin-right: 14px;
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
          }
          .rain-monitor-table-container-right-bottom-dd {
            width: 100%;
            padding-top: 16px;
            //   height: calc(100% - vh(45));

            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              height: 40px;
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 500;
              font-size: 22px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              // overflow: hidden;
              white-space: wrap;
              text-overflow: ellipsis;
            }
            .th-index1 {
              width: 16.66%;
            }
            .th-station1 {
              width: 16.66%;
            }
            .th-hour1 {
              width: 16.66%;
            }
            .th-warning1 {
              width: 16.66%;
            }
            .th-warning2 {
              width: 16.66%;
            }
            .th-warning3 {
              width: 16.66%;
            }

            .rain-monitor-table-body {
              height: 442px;
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              cursor: pointer;
              display: flex;
              align-items: center;
              min-height: vh(54);
              border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
              transition: background 0.2s;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: 24px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
              padding: 13px 0;
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(18);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }

          .diaodu-fl-w {
            display: flex;
            .charts-diaodu-fl {
              flex: 1;
              height: vh(310);
            }
          }
        }
        .yingji-fangyu {
          // width: 780px;
          flex: 1;
          .yftop {
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          }
          .p-s-x-t-w-r {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            padding-left: 20px;
            gap: 1px;
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
            .x-t-item {
              cursor: pointer;
              width: 200px;
              height: 110px;
              // display: flex;
              justify-content: space-around;
              align-items: center;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: center;
              // padding-left: 20px;
              .x-t-item-wrap {
                display: flex;
                justify-content: flex-start;
                img {
                  display: block;
                  width: 48px;
                  height: 48px;
                  margin-right: 12px;
                }
              }

              &:last-child {
                padding-left: 0;
              }
              .i-i-right {
                display: flex;
                align-items: baseline;
              }
              .item-i {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 350;
                font-size: 12px;
                color: #ffffff;
                line-height: 32px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
              .i-i-r-name {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 700;
                font-size: 20px;
                line-height: 41px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                background: linear-gradient(90deg, #ffffff 0%, #0098fa 100%);
                background-clip: text;
                color: transparent;
              }
              .i-i-r-n-v {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 350;
                font-size: 14px;
                color: #ffffff;
                line-height: 29px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin-left: 8px;
              }
            }
          }

          .fangxun-w-yuqing {
            // height: 515px;
            margin-left: 14px;
            // margin-top: 16px;
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
            .yuqing-i {
              height: 498px;
              overflow-y: auto;
              scrollbar-width: none;
            }
            .yuqing-w {
              min-height: 100px;
              padding: 20px 120px 0 20px;
              position: relative;
              border-bottom: 1px solid rgba(216, 216, 216, 0.2);
              .yuqing-u {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 500;
                font-size: 24px;
                color: #ffffff;
                line-height: 38px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
              .yuqing-b {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 350;
                font-size: 22px;
                color: #949fbc;
                line-height: 38px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
              .yuqing-status {
                position: absolute;
                right: 20px;
                top: 20px;
                width: 70px;
                height: 30px;
                background: rgba(0, 255, 30, 0.1);
                border-radius: 4px 4px 4px 4px;
                border: 1px solid #00ff1e;
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 500;
                font-size: 16px;
                color: #00ff1e;
                text-align: center;
                line-height: 30px;
                font-style: normal;
                text-transform: none;
              }
              .yuqing-status1 {
                position: absolute;
                right: 20px;
                top: 20px;
                width: 70px;
                height: 30px;
                background: rgba(255, 98, 0, 0.1);
                border-radius: 4px 4px 4px 4px;
                border: 1px solid #ff6200;
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 500;
                font-size: 16px;
                color: #ff6200;
                text-align: center;
                line-height: 30px;
                font-style: normal;
                text-transform: none;
              }
            }
          }
          .diaoduzhixingtop {
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.156) 100%);
            margin-bottom: 20px;
            .charts-diaodu-f {
              flex: 1;
              height: vh(310);
            }
          }
        }
        .zhongtai {
          width: 886px;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          .rain-monitor-table-container-right {
            width: 100%;
            margin-top: 26px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              height: 40px;
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 500;
              font-size: 22px;
              color: #ffffff;
              line-height: 38px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              // overflow: hidden;
              white-space: wrap;
              text-overflow: ellipsis;
            }
            .th-index1 {
              width: 14%;
            }
            .th-station1 {
              // padding-right: 20px;
              width: 43%;
            }
            .th-hour1 {
              width: 23%;
            }
            .th-warning1 {
              width: 20%;
            }

            .rain-monitor-table-body {
              height: vh(500);
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: 22px;
              color: #ffffff;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              display: flex;
              align-items: center;
              min-height: vh(54);
              padding: 8px 0;
              color: #fff;
              border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
              transition: background 0.2s;
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(22);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }
        }
      }
      .xietong {
        margin-top: 30px;

        .rain-monitor-table-container-right-bottom {
          width: 100%;
          padding-top: 26px;
          //   height: calc(100% - vh(45));

          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            align-items: center;
            height: 40px;
            background: #0d4873;
            border: 1px solid;
            border-image: linear-gradient(
                90deg,
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
              )
              1 1;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 22px;
            color: #ffffff;
            line-height: 38px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            // overflow: hidden;
            white-space: wrap;
            text-overflow: ellipsis;
          }
          .th-index1 {
            width: 16.66%;
          }
          .th-station1 {
            width: 16.66%;
          }
          .th-hour1 {
            width: 16.66%;
          }
          .th-warning1 {
            width: 16.66%;
          }
          .th-warning2 {
            width: 16.66%;
          }
          .th-warning3 {
            width: 16.66%;
          }

          .rain-monitor-table-body {
            height: 550px;
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            display: flex;
            align-items: center;
            min-height: vh(54);
            border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
            transition: background 0.2s;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 350;
            font-size: 22px;
            color: #ffffff;
            line-height: 38px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
      }
      .charts-diaodu {
        height: 300px;
        // border-bottom: 1px solid #aaa;
      }
    }
  }
}
</style>
<style lang="scss">
.supplies-dialog {
  pointer-events: auto;
  &.fangxunrenyuan {
    .el-dialog {
      border: 2px solid rgba(64, 228, 251, 0.3);
      .el-table__row {
        .cell {
          line-height: 32px;
        }
      }
    }
  }
  .el-overlay-dialog {
    pointer-events: auto;
    .el-dialog {
      pointer-events: auto;
    }
  }
  .yuqing-table-wrap {
    .el-table__row {
      .cell {
        line-height: 32px;
      }
    }
  }
}

.supplies-dialog {
  .heaader-close {
    position: absolute;
    top: 30px;
    right: 40px;
    width: 50px;
    height: 50px;
    color: #fff;
    font-size: 48px;
    // background: url('@/assets/images/dialognewclose.png') no-repeat center/100%;
    cursor: pointer;
  }
  .new-header-title {
    height: 90px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .header-name {
      position: relative;
      top: 8px;
      font-size: 28px;
      color: #fff;
    }
  }
  .el-dialog {
    background: rgba(26, 72, 123, 0.9693);
    background: url('@/assets/images/dialognew.png') no-repeat center/100%;
    // box-shadow: inset 0px 0px 57px 0px #0e4571;
    border-radius: 0px 0px 0px 0px;
    // border: 1px solid #4190d8;
    padding: 0;
    .el-dialog__header {
      // display: none;
      height: 50px;
      // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
      // border-radius: 0px 0px 0px 0px;
      padding-left: 30px;
      .el-dialog__title {
        line-height: 50px;
        color: #fff;
        font-size: 24px;
      }
      .el-dialog__close {
        color: #fff;
        font-size: 30px;
      }
    }
    .el-dialog__body {
      padding-left: 50px;
      padding-right: 50px;
      padding-bottom: 80px;
      .el-form-item__label {
        color: #fff;
        font-size: 22px;
      }
      .el-select__wrapper {
        background: transparent;
        box-shadow: 0 0 0 1px #4190d8 inset;
        .el-select__selected-item {
          color: #fff;
          font-size: 18px;
        }
      }
      .el-input__wrapper {
        background: transparent;
        box-shadow: 0 0 0 1px #4190d8 inset;
        .el-input__inner {
          color: #fff;
          font-size: 18px;
        }
        .el-input__inner::-webkit-input-placeholder {
          color: #fff;
        }
        .el-input__inner::-moz-placeholder {
          color: #fff;
        }
      }
      .el-button {
        background: rgba(0, 147, 255, 0.2);
        border: 1px solid #1f8ad4;
        font-family:
          Source Han Sans,
          Source Han Sans;
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .el-table__row {
        background: transparent;
        cursor: pointer;
        &:nth-child(even) {
          background: rgba(27, 114, 223, 0.2);
        }
        td {
          background: transparent;
          color: #fff;
          font-size: 22px;
          // border-bottom: 1px solid rgba(216, 216, 216, 0.2);
          border-bottom: none;
          padding: 16px 0;
        }
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background-color: rgba(31, 138, 212, 0.4);
      }
      .el-table__header-wrapper {
        tr {
          background: transparent;
          // background: rgba(52, 120, 187, 0.3632);
          // border-radius: 2px 2px 2px 2px;
          background: #1b72df;
          border-radius: 0px 0px 0px 0px;
          // border: 1px solid rgba(60, 139, 217, 0.4542);
          th {
            background: transparent;
            font-size: 20px;
            color: #fff;
            border-bottom: none;
          }
        }
      }

      .el-table--fit .el-table__inner-wrapper::before {
        width: 0px;
      }

      .el-pager {
        li {
          background: transparent;
          color: #d8d8d8;
          font-size: 18px;
          &.is-active {
            background: #008aff;
          }
        }
      }
      .el-pagination {
        float: right;
        button {
          background: transparent;
        }
        .btn-prev,
        .btn-next {
          color: #fff;
        }
        .el-pagination__total,
        .el-pagination__jump {
          color: #fff;
          font-size: 18px;
        }
      }
    }
  }
}

.select-popper {
  .el-select-dropdown__item {
    font-size: 18px;
  }
}
</style>
